cmake_minimum_required(VERSION 3.10)

set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR arm)

set(ARCH_FLAGS "-mcpu=cortex-r5 -mlittle-endian -mthumb -mfloat-abi=soft")

if(DEFINED ENV{CLANG_LLVM_TOOLCHAIN})
  set(CLANG_LLVM_TOOLCHAIN $ENV{CLANG_LLVM_TOOLCHAIN})
else()
  message(FATAL_ERROR "Please set environment variable \"CLANG_LLVM_TOOLCHAIN\" first.")
endif()
list(INSERT CMAKE_PROGRAM_PATH 0 ${CLANG_LLVM_TOOLCHAIN}/bin)

find_program(CMAKE_C_COMPILER NAMES clang)
set(CMAKE_C_FLAGS                "-Wall -std=c99 -ffunction-sections -fdata-sections -fno-builtin -fshort-enums ${ARCH_FLAGS}" CACHE STRING "c flags" )
set(CMAKE_C_FLAGS_DEBUG          "-ggdb -g3")
set(CMAKE_C_FLAGS_MINSIZEREL     "-Os -DNDEBUG")
set(CMAKE_C_FLAGS_RELEASE        "-O3 -DNDEBUG")
set(CMAKE_C_FLAGS_RELWITHDEBINFO "-O2 -g")

find_program(CMAKE_CXX_COMPILER NAMES clang++)
set(CMAKE_CXX_FLAGS                "-Wall -std=c++11 -fno-exceptions -fno-rtti -fno-threadsafe-statics -ffunction-sections -fdata-sections -fno-builtin -fshort-enums ${ARCH_FLAGS}" CACHE STRING "c++ flags" )
set(CMAKE_CXX_FLAGS_DEBUG          "-ggdb -g3")
set(CMAKE_CXX_FLAGS_MINSIZEREL     "-Os -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE        "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O2 -g")

set(CMAKE_ASM_COMPILER ${CMAKE_C_COMPILER})
set(CMAKE_ASM_FLAGS "-Wall ${ARCH_FLAGS} -x assembler-with-cpp" CACHE STRING "Common flags for assembler")
set(CMAKE_ASM_COMPILER_ID Clang)
set(CMAKE_EXE_LINKER_FLAGS "-static -lgcc -nostartfiles -Wl,--gc-sections -fuse-linker-plugin --specs=nano.specs -lc" CACHE STRING "")

find_program(CMAKE_AR NAMES llvm-ar)
find_program(CMAKE_NM NAMES llvm-nm)
find_program(CMAKE_OBJCOPY NAMES llvm-objcopy)
find_program(CMAKE_OBJDUMP NAMES llvm-objdump)
find_program(CMAKE_RANLIB NAMES llvm-ranlib)

if(DEFINED ENV{GCC_ARM_TOOLCHAIN})
  set(GCC_ARM_TOOLCHAIN $ENV{GCC_ARM_TOOLCHAIN})
else()
  message(FATAL_ERROR "Please set environment variable \"GCC_ARM_TOOLCHAIN\" first.")
endif()
list(INSERT CMAKE_PROGRAM_PATH 0 ${GCC_ARM_TOOLCHAIN}/bin)

# CMake generally calls CMAKE_C_COMPILER to link the executable. Clang invokes itself the linker installed on the host machine
find_program(CROSS_COMPILE_GCC arm-none-eabi-gcc)
find_program(CROSS_COMPILE_GXX arm-none-eabi-g++)
set(CMAKE_C_LINK_EXECUTABLE "${CROSS_COMPILE_GCC} <CMAKE_C_LINK_FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>")
set(CMAKE_CXX_LINK_EXECUTABLE "${CROSS_COMPILE_GXX} <CMAKE_CXX_LINK_FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>")

# Specify the cross compiler
# The target triple needs to match the prefix of the binutils exactly
# (e.g. CMake looks for arm-none-eabi-ar)
set(CLANG_TARGET_TRIPLE arm-none-eabi)
set(GCC_ARM_TOOLCHAIN_PREFIX ${CLANG_TARGET_TRIPLE})
set(CMAKE_C_COMPILER_TARGET ${CLANG_TARGET_TRIPLE})
set(CMAKE_CXX_COMPILER_TARGET ${CLANG_TARGET_TRIPLE})
set(CMAKE_ASM_COMPILER_TARGET ${CLANG_TARGET_TRIPLE})

# Find arelease and ninja
if(DEFINED ENV{MISC_TOOLS_PATH})
  set(MISC_TOOLS_PATH $ENV{MISC_TOOLS_PATH})
else()
  message(FATAL_ERROR "Please set environment variable \"MISC_TOOLS_PATH\" first.")
endif()
list(INSERT CMAKE_PROGRAM_PATH 0 ${MISC_TOOLS_PATH})
find_program(ARELEASE NAMES arelease)

# Don't run the linker on compiler check
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# C/C++ toolchain
set(GCC_ARM_SYSROOT "${GCC_ARM_TOOLCHAIN}/${GCC_ARM_TOOLCHAIN_PREFIX}")
set(CMAKE_SYSROOT ${GCC_ARM_SYSROOT})
set(CMAKE_FIND_ROOT_PATH ${GCC_ARM_SYSROOT})

# Search for programs in the build host directories
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
# For libraries and headers in the target directories
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
