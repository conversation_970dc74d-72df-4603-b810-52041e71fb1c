<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Deprecated List</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Deprecated List";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="org/libjpegturbo/turbojpeg/package-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#field">Deprecated Fields</a></li>
<li><a href="#method">Deprecated Methods</a></li>
<li><a href="#constructor">Deprecated Constructors</a></li>
</ul>
</div>
<div class="contentContainer"><a name="field">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Deprecated Fields table, listing deprecated fields, and an explanation">
<caption><span>Deprecated Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCEMMX">org.libjpegturbo.turbojpeg.TJ.FLAG_FORCEMMX</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCESSE">org.libjpegturbo.turbojpeg.TJ.FLAG_FORCESSE</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCESSE2">org.libjpegturbo.turbojpeg.TJ.FLAG_FORCESSE2</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCESSE3">org.libjpegturbo.turbojpeg.TJ.FLAG_FORCESSE3</a></td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Deprecated Methods table, listing deprecated methods, and an explanation">
<caption><span>Deprecated Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJ.html#bufSizeYUV(int,%20int,%20int)">org.libjpegturbo.turbojpeg.TJ.bufSizeYUV(int, int, int)</a>
<div class="block"><i>Use <a href="org/libjpegturbo/turbojpeg/TJ.html#bufSizeYUV(int,%20int,%20int,%20int)"><code>TJ.bufSizeYUV(int, int, int, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#compress(java.awt.image.BufferedImage,%20byte[],%20int)">org.libjpegturbo.turbojpeg.TJCompressor.compress(BufferedImage, byte[], int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#compress(byte[],%20int)"><code>TJCompressor.compress(byte[], int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#compress(java.awt.image.BufferedImage,%20int)">org.libjpegturbo.turbojpeg.TJCompressor.compress(BufferedImage, int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#compress(int)"><code>TJCompressor.compress(int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int)">org.libjpegturbo.turbojpeg.TJDecompressor.decompress(byte[], int, int, int, int, int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJDecompressor.decompress(byte[], int, int, int, int, int, int, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(byte[],%20int)">org.libjpegturbo.turbojpeg.TJDecompressor.decompressToYUV(byte[], int)</a>
<div class="block"><i>Use <a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>TJDecompressor.decompressToYUV(YUVImage, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int)">org.libjpegturbo.turbojpeg.TJDecompressor.decompressToYUV(int)</a>
<div class="block"><i>Use <a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int,%20int,%20int)"><code>TJDecompressor.decompressToYUV(int, int, int, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(java.awt.image.BufferedImage,%20byte[],%20int)">org.libjpegturbo.turbojpeg.TJCompressor.encodeYUV(BufferedImage, byte[], int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)"><code>TJCompressor.encodeYUV(byte[], int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(java.awt.image.BufferedImage,%20int)">org.libjpegturbo.turbojpeg.TJCompressor.encodeYUV(BufferedImage, int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>TJCompressor.encodeYUV(int, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)">org.libjpegturbo.turbojpeg.TJCompressor.encodeYUV(byte[], int)</a>
<div class="block"><i>Use <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>TJCompressor.encodeYUV(YUVImage, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int)">org.libjpegturbo.turbojpeg.TJCompressor.encodeYUV(int)</a>
<div class="block"><i>Use <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>TJCompressor.encodeYUV(int, int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#setJPEGImage(byte[],%20int)">org.libjpegturbo.turbojpeg.TJDecompressor.setJPEGImage(byte[], int)</a>
<div class="block"><i>Use <a href="org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(byte[],%20int)"><code>TJDecompressor.setSourceImage(byte[], int)</code></a> instead.</i></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int)">org.libjpegturbo.turbojpeg.TJCompressor.setSourceImage(byte[], int, int, int, int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="constructor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Deprecated Constructors table, listing deprecated constructors, and an explanation">
<caption><span>Deprecated Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int)">org.libjpegturbo.turbojpeg.TJCompressor(byte[], int, int, int, int)</a>
<div class="block"><i>Use
 <a href="org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJCompressor.TJCompressor(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="org/libjpegturbo/turbojpeg/package-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
