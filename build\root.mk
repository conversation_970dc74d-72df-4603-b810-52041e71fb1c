#
# Makefile
#

include build/version.mk

ROOT_DIR = ${shell pwd}
OUT ?= $(ROOT_DIR)/out

ifeq ($(CUSTOM_PRODUCT),)
  $(info ######## No CUSTOM_PRODUCT defined, use default definition ########)
else
  $(info ######## CUSTOM_PRODUCT=$(CUSTOM_PRODUCT) ########)
endif

#
# PS_MODE = { LTEONLY, LTEGSM, LWG }
#
export PS_MODE ?= LTEGSM
ifneq ($(PS_MODE),LTEGSM)
  ifneq ($(PS_MODE),LTEONLY)
    ifneq ($(PS_MODE),LWG)
      ifneq ($(PS_MODE),LITE_LTEONLY)
        $(error The wrong PS mode specified "$(PS_MODE)")
      endif
    endif
  endif
endif

#
# TARGET_OS = { THREADX, ALIOS }
#
export TARGET_OS ?= THREADX
ifneq ($(TARGET_OS),THREADX)
  ifneq ($(TARGET_OS),ALIOS)
    $(error The wrong target os specified "$(TARGET_OS)")
  endif
endif

#
# CHIP_ID = { CRANE, CRANEG, CRANEL, CRANEM }
#
ifeq (,$(CHIP_ID))
  ifneq (,$(findstring craneg_modem,$(MAKECMDGOALS)))
    export CHIP_ID := CRANEG
  else ifneq (,$(findstring cranem_modem,$(MAKECMDGOALS)))
    export CHIP_ID := CRANEM
  else
    export CHIP_ID := CRANE
  endif
else
  ifneq ($(CHIP_ID),CRANE)
    ifneq ($(CHIP_ID),CRANEG)
      ifneq ($(CHIP_ID),CRANEL)
        ifneq ($(CHIP_ID),CRANEM)
          $(error The wrong chip id specified "$(CHIP_ID)")
        endif
      endif
    endif
  endif
endif
$(info Build with PS_MODE=$(PS_MODE) TARGET_OS=$(TARGET_OS) CHIP_ID=$(CHIP_ID) OUT=$(OUT))


OS := ${shell uname -o}
ifeq ($(OS),GNU/Linux)
  PLATFORM := linux-x86
else ifeq ($(OS),Msys)
  PLATFORM := windows-x86
else
  $(error The os "$(OS)" is not supported)
endif

export GCC_ARM_TOOLCHAIN := $(ROOT_DIR)/prebuilts/gcc_arm_none_eabi/$(PLATFORM)
export CLANG_LLVM_TOOLCHAIN := $(ROOT_DIR)/prebuilts/clang+llvm/$(PLATFORM)
export MISC_TOOLS_PATH := $(ROOT_DIR)/prebuilts/misc/$(PLATFORM)
export SDL2_PATH := $(ROOT_DIR)/prebuilts/sdl/$(PLATFORM)

NINJA = $(ROOT_DIR)/prebuilts/misc/$(PLATFORM)/ninja
CMAKE = $(ROOT_DIR)/prebuilts/cmake/$(PLATFORM)/bin/cmake
SDK_BUILD_PATH = $(realpath $(dir $(CMAKE))):$(realpath $(dir $(NINJA))):$(PATH)
MKDIR = mkdir

PRODUCTS := $(sort $(dir $(wildcard product/*/)))
PRODUCTS := ${shell echo $(PRODUCTS) | sed -e 's|product||g' -e 's|/||g'}
VARIANTS = phone watch watch_sport
BUILD_NINJA = build.ninja


VERBOSE = 0
ifeq ($(VERBOSE),1)
  V = -v
else
  V =
endif

all:

ifeq ($(OS),Msys)
cranem_modem_sdk:
	@cd product/cranem_modem && export PATH="$(SDK_BUILD_PATH)" && ./build.bat

craneg_modem_sdk:
	@cd product/craneg_modem && export PATH="$(SDK_BUILD_PATH)" && ./build.bat
endif

define make-product-target
  $$(OUT)/product/$(1)_$(2)/$$(BUILD_NINJA):
	@$$(MKDIR) -p $$(dir $$@)
	@cd $$(dir $$@) && $$(CMAKE) -DVARIANT=$(2) \
		-DCMAKE_TOOLCHAIN_FILE=$$(ROOT_DIR)/build/toolchain_$(1).cmake \
		-DCRANE_PLATFORM_VERSION=$(CRANE_PLATFORM_VERSION) \
		-G Ninja \
		$$(ROOT_DIR)/product/$(1)

  $(1)_$(2): $$(OUT)/product/$(1)_$(2)/$$(BUILD_NINJA)
	@$$(NINJA) -C $$(dir $$<) $(V)

  all: $(1)_$(2)
  .PHONY: $(1)_$(2)
endef

$(foreach product,$(PRODUCTS),$(foreach variant,$(VARIANTS),$(eval $(call make-product-target,$(product),$(variant)))))

.PHONY: clean all

clean:
	rm -rf $(OUT)
	[ -x "$$(command -v git)" ] && (cd product/cranem_modem; git clean -fdx) || true
	[ -x "$$(command -v git)" ] && (cd product/craneg_modem; git clean -fdx) || true
