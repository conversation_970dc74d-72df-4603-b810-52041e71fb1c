<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>TJTransform</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TJTransform";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/libjpegturbo/turbojpeg/TJTransform.html" target="_top">Frames</a></li>
<li><a href="TJTransform.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested_classes_inherited_from_class_java.awt.geom.Rectangle2D">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods_inherited_from_class_java.awt.Rectangle">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.libjpegturbo.turbojpeg</div>
<h2 title="Class TJTransform" class="title">Class TJTransform</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.geom.RectangularShape</li>
<li>
<ul class="inheritance">
<li>java.awt.geom.Rectangle2D</li>
<li>
<ul class="inheritance">
<li>java.awt.Rectangle</li>
<li>
<ul class="inheritance">
<li>org.libjpegturbo.turbojpeg.TJTransform</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.Shape, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">TJTransform</span>
extends java.awt.Rectangle</pre>
<div class="block">Lossless transform parameters</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../serialized-form.html#org.libjpegturbo.turbojpeg.TJTransform">Serialized Form</a></dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested_class_summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested_classes_inherited_from_class_java.awt.geom.Rectangle2D">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.geom.Rectangle2D</h3>
<code>java.awt.geom.Rectangle2D.Double, java.awt.geom.Rectangle2D.Float</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a></code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#cf">cf</a></strong></code>
<div class="block">Custom filter instance</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#NUMOP">NUMOP</a></strong></code>
<div class="block">The number of lossless transform operations</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#op">op</a></strong></code>
<div class="block">Transform operation (one of <code>OP_*</code>)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_HFLIP">OP_HFLIP</a></strong></code>
<div class="block">Flip (mirror) image horizontally.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_NONE">OP_NONE</a></strong></code>
<div class="block">Do not transform the position of the image pixels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_ROT180">OP_ROT180</a></strong></code>
<div class="block">Rotate image 180 degrees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_ROT270">OP_ROT270</a></strong></code>
<div class="block">Rotate image counter-clockwise by 90 degrees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_ROT90">OP_ROT90</a></strong></code>
<div class="block">Rotate image clockwise by 90 degrees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_TRANSPOSE">OP_TRANSPOSE</a></strong></code>
<div class="block">Transpose image (flip/mirror along upper left to lower right axis).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_TRANSVERSE">OP_TRANSVERSE</a></strong></code>
<div class="block">Transverse transpose image (flip/mirror along upper right to lower left
 axis).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OP_VFLIP">OP_VFLIP</a></strong></code>
<div class="block">Flip (mirror) image vertically.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_COPYNONE">OPT_COPYNONE</a></strong></code>
<div class="block">This option will prevent <a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> from copying any extra markers (including EXIF
 and ICC profile data) from the source image to the output image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_CROP">OPT_CROP</a></strong></code>
<div class="block">This option will enable lossless cropping.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_GRAY">OPT_GRAY</a></strong></code>
<div class="block">This option will discard the color data in the input image and produce
 a grayscale output image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_NOOUTPUT">OPT_NOOUTPUT</a></strong></code>
<div class="block">This option will prevent <a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> from outputting a JPEG image for this
 particular transform.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT">OPT_PERFECT</a></strong></code>
<div class="block">This option will cause <a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> to throw an exception if the transform is not
 perfect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PROGRESSIVE">OPT_PROGRESSIVE</a></strong></code>
<div class="block">This option will enable progressive entropy coding in the output image
 generated by this particular transform.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_TRIM">OPT_TRIM</a></strong></code>
<div class="block">This option will discard any partial MCU blocks that cannot be
 transformed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#options">options</a></strong></code>
<div class="block">Transform options (bitwise OR of one or more of <code>OPT_*</code>)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_java.awt.Rectangle">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Rectangle</h3>
<code>height, width, x, y</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_java.awt.geom.Rectangle2D">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.geom.Rectangle2D</h3>
<code>OUT_BOTTOM, OUT_LEFT, OUT_RIGHT, OUT_TOP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform()">TJTransform</a></strong>()</code>
<div class="block">Create a new lossless transform instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform(int,%20int,%20int,%20int,%20int,%20int,%20org.libjpegturbo.turbojpeg.TJCustomFilter)">TJTransform</a></strong>(int&nbsp;x,
           int&nbsp;y,
           int&nbsp;w,
           int&nbsp;h,
           int&nbsp;op,
           int&nbsp;options,
           <a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a>&nbsp;cf)</code>
<div class="block">Create a new lossless transform instance with the given parameters.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform(java.awt.Rectangle,%20int,%20int,%20org.libjpegturbo.turbojpeg.TJCustomFilter)">TJTransform</a></strong>(java.awt.Rectangle&nbsp;r,
           int&nbsp;op,
           int&nbsp;options,
           <a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a>&nbsp;cf)</code>
<div class="block">Create a new lossless transform instance with the given parameters.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.awt.Rectangle">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Rectangle</h3>
<code>add, add, add, contains, contains, contains, contains, createIntersection, createUnion, equals, getBounds, getBounds2D, getHeight, getLocation, getSize, getWidth, getX, getY, grow, inside, intersection, intersects, isEmpty, move, outcode, reshape, resize, setBounds, setBounds, setLocation, setLocation, setRect, setSize, setSize, toString, translate, union</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.awt.geom.Rectangle2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.geom.Rectangle2D</h3>
<code>add, add, add, contains, contains, getPathIterator, getPathIterator, hashCode, intersect, intersects, intersectsLine, intersectsLine, outcode, setFrame, setRect, union</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.awt.geom.RectangularShape">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.geom.RectangularShape</h3>
<code>clone, contains, contains, getCenterX, getCenterY, getFrame, getMaxX, getMaxY, getMinX, getMinY, intersects, setFrame, setFrame, setFrameFromCenter, setFrameFromCenter, setFrameFromDiagonal, setFrameFromDiagonal</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.awt.Shape">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.awt.Shape</h3>
<code>contains, contains, contains, contains, getPathIterator, getPathIterator, intersects, intersects</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NUMOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMOP</h4>
<pre>public static final&nbsp;int NUMOP</pre>
<div class="block">The number of lossless transform operations</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.NUMOP">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_NONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_NONE</h4>
<pre>public static final&nbsp;int OP_NONE</pre>
<div class="block">Do not transform the position of the image pixels.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_NONE">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_HFLIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_HFLIP</h4>
<pre>public static final&nbsp;int OP_HFLIP</pre>
<div class="block">Flip (mirror) image horizontally.  This transform is imperfect if there
 are any partial MCU blocks on the right edge.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_HFLIP">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_VFLIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_VFLIP</h4>
<pre>public static final&nbsp;int OP_VFLIP</pre>
<div class="block">Flip (mirror) image vertically.  This transform is imperfect if there are
 any partial MCU blocks on the bottom edge.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_VFLIP">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_TRANSPOSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_TRANSPOSE</h4>
<pre>public static final&nbsp;int OP_TRANSPOSE</pre>
<div class="block">Transpose image (flip/mirror along upper left to lower right axis).  This
 transform is always perfect.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_TRANSPOSE">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_TRANSVERSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_TRANSVERSE</h4>
<pre>public static final&nbsp;int OP_TRANSVERSE</pre>
<div class="block">Transverse transpose image (flip/mirror along upper right to lower left
 axis).  This transform is imperfect if there are any partial MCU blocks in
 the image.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_TRANSVERSE">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_ROT90">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_ROT90</h4>
<pre>public static final&nbsp;int OP_ROT90</pre>
<div class="block">Rotate image clockwise by 90 degrees.  This transform is imperfect if
 there are any partial MCU blocks on the bottom edge.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_ROT90">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_ROT180">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_ROT180</h4>
<pre>public static final&nbsp;int OP_ROT180</pre>
<div class="block">Rotate image 180 degrees.  This transform is imperfect if there are any
 partial MCU blocks in the image.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_ROT180">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OP_ROT270">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OP_ROT270</h4>
<pre>public static final&nbsp;int OP_ROT270</pre>
<div class="block">Rotate image counter-clockwise by 90 degrees.  This transform is imperfect
 if there are any partial MCU blocks on the right edge.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT"><code>OPT_PERFECT</code></a>, 
<a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OP_ROT270">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_PERFECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_PERFECT</h4>
<pre>public static final&nbsp;int OPT_PERFECT</pre>
<div class="block">This option will cause <a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> to throw an exception if the transform is not
 perfect.  Lossless transforms operate on MCU blocks, whose size depends on
 the level of chrominance subsampling used.  If the image's width or height
 is not evenly divisible by the MCU block size (see <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#getMCUWidth(int)"><code>TJ.getMCUWidth(int)</code></a>
 and <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#getMCUHeight(int)"><code>TJ.getMCUHeight(int)</code></a>), then there will be partial MCU blocks on the
 right and/or bottom edges.   It is not possible to move these partial MCU
 blocks to the top or left of the image, so any transform that would
 require that is "imperfect."  If this option is not specified, then any
 partial MCU blocks that cannot be transformed will be left in place, which
 will create odd-looking strips on the right or bottom edge of the image.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_PERFECT">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_TRIM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_TRIM</h4>
<pre>public static final&nbsp;int OPT_TRIM</pre>
<div class="block">This option will discard any partial MCU blocks that cannot be
 transformed.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_TRIM">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_CROP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_CROP</h4>
<pre>public static final&nbsp;int OPT_CROP</pre>
<div class="block">This option will enable lossless cropping.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_CROP">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_GRAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_GRAY</h4>
<pre>public static final&nbsp;int OPT_GRAY</pre>
<div class="block">This option will discard the color data in the input image and produce
 a grayscale output image.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_GRAY">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_NOOUTPUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_NOOUTPUT</h4>
<pre>public static final&nbsp;int OPT_NOOUTPUT</pre>
<div class="block">This option will prevent <a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> from outputting a JPEG image for this
 particular transform.  This can be used in conjunction with a custom
 filter to capture the transformed DCT coefficients without transcoding
 them.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_NOOUTPUT">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_PROGRESSIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_PROGRESSIVE</h4>
<pre>public static final&nbsp;int OPT_PROGRESSIVE</pre>
<div class="block">This option will enable progressive entropy coding in the output image
 generated by this particular transform.  Progressive entropy coding will
 generally improve compression relative to baseline entropy coding (the
 default), but it will reduce compression and decompression performance
 considerably.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_PROGRESSIVE">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="OPT_COPYNONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPT_COPYNONE</h4>
<pre>public static final&nbsp;int OPT_COPYNONE</pre>
<div class="block">This option will prevent <a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> from copying any extra markers (including EXIF
 and ICC profile data) from the source image to the output image.</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../constant-values.html#org.libjpegturbo.turbojpeg.TJTransform.OPT_COPYNONE">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="op">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>op</h4>
<pre>public&nbsp;int op</pre>
<div class="block">Transform operation (one of <code>OP_*</code>)</div>
</li>
</ul>
<a name="options">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>options</h4>
<pre>public&nbsp;int options</pre>
<div class="block">Transform options (bitwise OR of one or more of <code>OPT_*</code>)</div>
</li>
</ul>
<a name="cf">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>cf</h4>
<pre>public&nbsp;<a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a> cf</pre>
<div class="block">Custom filter instance</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TJTransform()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJTransform</h4>
<pre>public&nbsp;TJTransform()</pre>
<div class="block">Create a new lossless transform instance.</div>
</li>
</ul>
<a name="TJTransform(int, int, int, int, int, int, org.libjpegturbo.turbojpeg.TJCustomFilter)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJTransform</h4>
<pre>public&nbsp;TJTransform(int&nbsp;x,
           int&nbsp;y,
           int&nbsp;w,
           int&nbsp;h,
           int&nbsp;op,
           int&nbsp;options,
           <a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a>&nbsp;cf)</pre>
<div class="block">Create a new lossless transform instance with the given parameters.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>x</code> - the left boundary of the cropping region.  This must be evenly
 divisible by the MCU block width (see <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#getMCUWidth(int)"><code>TJ.getMCUWidth(int)</code></a>)</dd><dd><code>y</code> - the upper boundary of the cropping region.  This must be evenly
 divisible by the MCU block height (see <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#getMCUHeight(int)"><code>TJ.getMCUHeight(int)</code></a>)</dd><dd><code>w</code> - the width of the cropping region.  Setting this to 0 is the
 equivalent of setting it to (width of the source JPEG image -
 <code>x</code>).</dd><dd><code>h</code> - the height of the cropping region.  Setting this to 0 is the
 equivalent of setting it to (height of the source JPEG image -
 <code>y</code>).</dd><dd><code>op</code> - one of the transform operations (<code>OP_*</code>)</dd><dd><code>options</code> - the bitwise OR of one or more of the transform options
 (<code>OPT_*</code>)</dd><dd><code>cf</code> - an instance of an object that implements the <a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><code>TJCustomFilter</code></a> interface, or null if no custom filter is needed</dd></dl>
</li>
</ul>
<a name="TJTransform(java.awt.Rectangle, int, int, org.libjpegturbo.turbojpeg.TJCustomFilter)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TJTransform</h4>
<pre>public&nbsp;TJTransform(java.awt.Rectangle&nbsp;r,
           int&nbsp;op,
           int&nbsp;options,
           <a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a>&nbsp;cf)</pre>
<div class="block">Create a new lossless transform instance with the given parameters.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>r</code> - a <code>Rectangle</code> instance that specifies the cropping
 region.  See <a href="../../../org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform(int,%20int,%20int,%20int,%20int,%20int,%20org.libjpegturbo.turbojpeg.TJCustomFilter)"><code>TJTransform(int, int, int, int, int, int, TJCustomFilter)</code></a> for more
 detail.</dd><dd><code>op</code> - one of the transform operations (<code>OP_*</code>)</dd><dd><code>options</code> - the bitwise OR of one or more of the transform options
 (<code>OPT_*</code>)</dd><dd><code>cf</code> - an instance of an object that implements the <a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><code>TJCustomFilter</code></a> interface, or null if no custom filter is needed</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/libjpegturbo/turbojpeg/TJTransform.html" target="_top">Frames</a></li>
<li><a href="TJTransform.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested_classes_inherited_from_class_java.awt.geom.Rectangle2D">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods_inherited_from_class_java.awt.Rectangle">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
