TURBOJPEG_1.0
{
  global:
    tjInitCompress;
    tjCompress;
    TJBUFSIZE;
    tjInitDecompress;
    tjDecompressHeader;
    tjDecompress;
    tjD<PERSON>roy;
    tjGetErrorStr;
  local:
    *;
};

TURBOJPEG_1.1
{
  global:
    TJBUFSIZEYUV;
    tjDecompressHeader2;
    tjDecompressToYUV;
    tjEncodeYUV;
} TURBOJPEG_1.0;

TURBOJPEG_1.2
{
  global:
    tjAlloc;
    tjBufSize;
    tjBufSizeYUV;
    tjCompress2;
    tjDecompress2;
    tjEncodeYUV2;
    tjFree;
    tjGetScalingFactors;
    tjInitTransform;
    tjTransform;
    Java_org_libjpegturbo_turbojpeg_TJ_bufSize;
    Java_org_libjpegturbo_turbojpeg_TJ_bufSizeYUV__III;
    Java_org_libjpegturbo_turbojpeg_TJ_getScalingFactors;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_init;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress___3BIIII_3BIII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress___3IIIII_3BIII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_encodeYUV___3BIIII_3BII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_encodeYUV___3IIIII_3BII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_destroy;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_init;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompressHeader;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress___3BI_3BIIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress___3BI_3IIIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompressToYUV___3BI_3BI;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_destroy;
    Java_org_libjpegturbo_turbojpeg_TJTransformer_init;
    Java_org_libjpegturbo_turbojpeg_TJTransformer_transform;
} TURBOJPEG_1.1;

TURBOJPEG_1.3
{
  global:
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress___3BIIIIII_3BIII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress___3IIIIIII_3BIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress___3BI_3BIIIIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress___3BI_3IIIIIIII;
} TURBOJPEG_1.2;

TURBOJPEG_1.4
{
  global:
    tjBufSizeYUV2;
    tjCompressFromYUV;
    tjCompressFromYUVPlanes;
    tjDecodeYUV;
    tjDecodeYUVPlanes;
    tjDecompressHeader3;
    tjDecompressToYUV2;
    tjDecompressToYUVPlanes;
    tjEncodeYUV3;
    tjEncodeYUVPlanes;
    tjPlaneHeight;
    tjPlaneSizeYUV;
    tjPlaneWidth;
    Java_org_libjpegturbo_turbojpeg_TJ_bufSizeYUV__IIII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compressFromYUV___3_3B_3II_3III_3BII;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_encodeYUV___3BIIIIII_3_3B_3I_3III;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_encodeYUV___3IIIIIII_3_3B_3I_3III;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompressToYUV___3BI_3_3B_3II_3III;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decodeYUV___3_3B_3I_3II_3BIIIIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decodeYUV___3_3B_3I_3II_3IIIIIIII;
    Java_org_libjpegturbo_turbojpeg_TJ_planeHeight__III;
    Java_org_libjpegturbo_turbojpeg_TJ_planeSizeYUV__IIIII;
    Java_org_libjpegturbo_turbojpeg_TJ_planeWidth__III;
} TURBOJPEG_1.3;

TURBOJPEG_2.0
{
  global:
    tjGetErrorCode;
    tjGetErrorStr2;
    tjLoadImage;
    tjSaveImage;
} TURBOJPEG_1.4;
