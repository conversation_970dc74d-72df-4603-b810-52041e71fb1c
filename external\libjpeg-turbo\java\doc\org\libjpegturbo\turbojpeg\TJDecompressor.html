<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>TJDecompressor</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TJDecompressor";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/libjpegturbo/turbojpeg/TJDecompressor.html" target="_top">Frames</a></li>
<li><a href="TJDecompressor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.libjpegturbo.turbojpeg</div>
<h2 title="Class TJDecompressor" class="title">Class TJDecompressor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.libjpegturbo.turbojpeg.TJDecompressor</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Closeable, java.lang.AutoCloseable</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">TJDecompressor</span>
extends java.lang.Object
implements java.io.Closeable</pre>
<div class="block">TurboJPEG decompressor</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected long</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#handle">handle</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegBuf">jpegBuf</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegBufSize">jpegBufSize</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegColorspace">jpegColorspace</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegHeight">jpegHeight</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegSubsamp">jpegSubsamp</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegWidth">jpegWidth</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#yuvImage">yuvImage</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor()">TJDecompressor</a></strong>()</code>
<div class="block">Create a TurboJPEG decompresssor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor(byte[])">TJDecompressor</a></strong>(byte[]&nbsp;jpegImage)</code>
<div class="block">Create a TurboJPEG decompressor instance and associate the JPEG source
 image stored in <code>jpegImage</code> with the newly created instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor(byte[],%20int)">TJDecompressor</a></strong>(byte[]&nbsp;jpegImage,
              int&nbsp;imageSize)</code>
<div class="block">Create a TurboJPEG decompressor instance and associate the JPEG source
 image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with the newly created instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor(org.libjpegturbo.turbojpeg.YUVImage)">TJDecompressor</a></strong>(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;yuvImage)</code>
<div class="block">Create a TurboJPEG decompressor instance and associate the YUV planar
 source image stored in <code>yuvImage</code> with the newly created
 instance.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#close()">close</a></strong>()</code>
<div class="block">Free the native structures associated with this decompressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(java.awt.image.BufferedImage,%20int)">decompress</a></strong>(java.awt.image.BufferedImage&nbsp;dstImage,
          int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a decompressed/decoded image to
 the given <code>BufferedImage</code> instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int)">decompress</a></strong>(byte[]&nbsp;dstBuf,
          int&nbsp;desiredWidth,
          int&nbsp;pitch,
          int&nbsp;desiredHeight,
          int&nbsp;pixelFormat,
          int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)">decompress</a></strong>(byte[]&nbsp;dstBuf,
          int&nbsp;x,
          int&nbsp;y,
          int&nbsp;desiredWidth,
          int&nbsp;pitch,
          int&nbsp;desiredHeight,
          int&nbsp;pixelFormat,
          int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a grayscale, RGB, or CMYK image
 to the given destination buffer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(int[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)">decompress</a></strong>(int[]&nbsp;dstBuf,
          int&nbsp;x,
          int&nbsp;y,
          int&nbsp;desiredWidth,
          int&nbsp;stride,
          int&nbsp;desiredHeight,
          int&nbsp;pixelFormat,
          int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a grayscale, RGB, or CMYK image
 to the given destination buffer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(int,%20int,%20int,%20int)">decompress</a></strong>(int&nbsp;desiredWidth,
          int&nbsp;desiredHeight,
          int&nbsp;bufferedImageType,
          int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and return a <code>BufferedImage</code>
 instance containing the decompressed/decoded image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(int,%20int,%20int,%20int,%20int)">decompress</a></strong>(int&nbsp;desiredWidth,
          int&nbsp;pitch,
          int&nbsp;desiredHeight,
          int&nbsp;pixelFormat,
          int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance and return a buffer containing the decompressed image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(byte[],%20int)">decompressToYUV</a></strong>(byte[]&nbsp;dstBuf,
               int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>decompressToYUV(YUVImage, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int)">decompressToYUV</a></strong>(int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int,%20int,%20int)"><code>decompressToYUV(int, int, int, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int[],%20int,%20int)">decompressToYUV</a></strong>(int&nbsp;desiredWidth,
               int[]&nbsp;strides,
               int&nbsp;desiredHeight,
               int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a set of Y, U (Cb), and V (Cr) image planes and return a
 <code>YUVImage</code> instance containing the decompressed image planes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int,%20int,%20int)">decompressToYUV</a></strong>(int&nbsp;desiredWidth,
               int&nbsp;pad,
               int&nbsp;desiredHeight,
               int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a unified YUV planar image buffer and return a
 <code>YUVImage</code> instance containing the decompressed image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)">decompressToYUV</a></strong>(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;dstImage,
               int&nbsp;flags)</code>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a YUV planar image and store it in the given
 <code>YUVImage</code> instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#finalize()">finalize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getColorspace()">getColorspace</a></strong>()</code>
<div class="block">Returns the colorspace used in the source image (JPEG or YUV) associated
 with this decompressor instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getHeight()">getHeight</a></strong>()</code>
<div class="block">Returns the height of the source image (JPEG or YUV) associated with this
 decompressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getJPEGBuf()">getJPEGBuf</a></strong>()</code>
<div class="block">Returns the JPEG image buffer associated with this decompressor instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getJPEGSize()">getJPEGSize</a></strong>()</code>
<div class="block">Returns the size of the JPEG image (in bytes) associated with this
 decompressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledHeight(int,%20int)">getScaledHeight</a></strong>(int&nbsp;desiredWidth,
               int&nbsp;desiredHeight)</code>
<div class="block">Returns the height of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledWidth(int,%20int)">getScaledWidth</a></strong>(int&nbsp;desiredWidth,
              int&nbsp;desiredHeight)</code>
<div class="block">Returns the width of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getSubsamp()">getSubsamp</a></strong>()</code>
<div class="block">Returns the level of chrominance subsampling used in the source image
 (JPEG or YUV) associated with this decompressor instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getWidth()">getWidth</a></strong>()</code>
<div class="block">Returns the width of the source image (JPEG or YUV) associated with this
 decompressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#setJPEGImage(byte[],%20int)">setJPEGImage</a></strong>(byte[]&nbsp;jpegImage,
            int&nbsp;imageSize)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(byte[],%20int)"><code>setSourceImage(byte[], int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(byte[],%20int)">setSourceImage</a></strong>(byte[]&nbsp;jpegImage,
              int&nbsp;imageSize)</code>
<div class="block">Associate the JPEG image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with this decompressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(org.libjpegturbo.turbojpeg.YUVImage)">setSourceImage</a></strong>(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;srcImage)</code>
<div class="block">Associate the specified YUV planar source image with this decompressor
 instance.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="handle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>handle</h4>
<pre>protected&nbsp;long handle</pre>
</li>
</ul>
<a name="jpegBuf">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpegBuf</h4>
<pre>protected&nbsp;byte[] jpegBuf</pre>
</li>
</ul>
<a name="jpegBufSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpegBufSize</h4>
<pre>protected&nbsp;int jpegBufSize</pre>
</li>
</ul>
<a name="yuvImage">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>yuvImage</h4>
<pre>protected&nbsp;<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a> yuvImage</pre>
</li>
</ul>
<a name="jpegWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpegWidth</h4>
<pre>protected&nbsp;int jpegWidth</pre>
</li>
</ul>
<a name="jpegHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpegHeight</h4>
<pre>protected&nbsp;int jpegHeight</pre>
</li>
</ul>
<a name="jpegSubsamp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jpegSubsamp</h4>
<pre>protected&nbsp;int jpegSubsamp</pre>
</li>
</ul>
<a name="jpegColorspace">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>jpegColorspace</h4>
<pre>protected&nbsp;int jpegColorspace</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TJDecompressor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJDecompressor</h4>
<pre>public&nbsp;TJDecompressor()
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG decompresssor instance.</div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="TJDecompressor(byte[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJDecompressor</h4>
<pre>public&nbsp;TJDecompressor(byte[]&nbsp;jpegImage)
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG decompressor instance and associate the JPEG source
 image stored in <code>jpegImage</code> with the newly created instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>jpegImage</code> - JPEG image buffer (size of the JPEG image is assumed to
 be the length of the array.)  This buffer is not modified.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="TJDecompressor(byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJDecompressor</h4>
<pre>public&nbsp;TJDecompressor(byte[]&nbsp;jpegImage,
              int&nbsp;imageSize)
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG decompressor instance and associate the JPEG source
 image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with the newly created instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>jpegImage</code> - JPEG image buffer.  This buffer is not modified.</dd><dd><code>imageSize</code> - size of the JPEG image (in bytes)</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="TJDecompressor(org.libjpegturbo.turbojpeg.YUVImage)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TJDecompressor</h4>
<pre>public&nbsp;TJDecompressor(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;yuvImage)
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG decompressor instance and associate the YUV planar
 source image stored in <code>yuvImage</code> with the newly created
 instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>yuvImage</code> - <a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg"><code>YUVImage</code></a> instance containing a YUV planar
 image to be decoded.  This image is not modified.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setSourceImage(byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceImage</h4>
<pre>public&nbsp;void&nbsp;setSourceImage(byte[]&nbsp;jpegImage,
                  int&nbsp;imageSize)
                    throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Associate the JPEG image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with this decompressor instance.  This image will
 be used as the source image for subsequent decompress operations.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>jpegImage</code> - JPEG image buffer.  This buffer is not modified.</dd><dd><code>imageSize</code> - size of the JPEG image (in bytes)</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="setJPEGImage(byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJPEGImage</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;setJPEGImage(byte[]&nbsp;jpegImage,
                           int&nbsp;imageSize)
                  throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(byte[],%20int)"><code>setSourceImage(byte[], int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="setSourceImage(org.libjpegturbo.turbojpeg.YUVImage)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceImage</h4>
<pre>public&nbsp;void&nbsp;setSourceImage(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;srcImage)</pre>
<div class="block">Associate the specified YUV planar source image with this decompressor
 instance.  Subsequent decompress operations will decode this image into an
 RGB or grayscale destination image.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>srcImage</code> - <a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg"><code>YUVImage</code></a> instance containing a YUV planar image to
 be decoded.  This image is not modified.</dd></dl>
</li>
</ul>
<a name="getWidth()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;int&nbsp;getWidth()</pre>
<div class="block">Returns the width of the source image (JPEG or YUV) associated with this
 decompressor instance.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the width of the source image (JPEG or YUV) associated with this
 decompressor instance.</dd></dl>
</li>
</ul>
<a name="getHeight()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;int&nbsp;getHeight()</pre>
<div class="block">Returns the height of the source image (JPEG or YUV) associated with this
 decompressor instance.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the height of the source image (JPEG or YUV) associated with this
 decompressor instance.</dd></dl>
</li>
</ul>
<a name="getSubsamp()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubsamp</h4>
<pre>public&nbsp;int&nbsp;getSubsamp()</pre>
<div class="block">Returns the level of chrominance subsampling used in the source image
 (JPEG or YUV) associated with this decompressor instance.  See
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#SAMP_444"><code>TJ.SAMP_*</code></a>.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the level of chrominance subsampling used in the source image
 (JPEG or YUV) associated with this decompressor instance.</dd></dl>
</li>
</ul>
<a name="getColorspace()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColorspace</h4>
<pre>public&nbsp;int&nbsp;getColorspace()</pre>
<div class="block">Returns the colorspace used in the source image (JPEG or YUV) associated
 with this decompressor instance.  See <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#CS_RGB"><code>TJ.CS_*</code></a>.  If the
 source image is YUV, then this always returns <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#CS_YCbCr"><code>TJ.CS_YCbCr</code></a>.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the colorspace used in the source image (JPEG or YUV) associated
 with this decompressor instance.</dd></dl>
</li>
</ul>
<a name="getJPEGBuf()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJPEGBuf</h4>
<pre>public&nbsp;byte[]&nbsp;getJPEGBuf()</pre>
<div class="block">Returns the JPEG image buffer associated with this decompressor instance.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the JPEG image buffer associated with this decompressor instance.</dd></dl>
</li>
</ul>
<a name="getJPEGSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJPEGSize</h4>
<pre>public&nbsp;int&nbsp;getJPEGSize()</pre>
<div class="block">Returns the size of the JPEG image (in bytes) associated with this
 decompressor instance.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the size of the JPEG image (in bytes) associated with this
 decompressor instance.</dd></dl>
</li>
</ul>
<a name="getScaledWidth(int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaledWidth</h4>
<pre>public&nbsp;int&nbsp;getScaledWidth(int&nbsp;desiredWidth,
                 int&nbsp;desiredHeight)</pre>
<div class="block">Returns the width of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>desiredWidth</code> - desired width (in pixels) of the decompressed image.
 Setting this to 0 is the same as setting it to the width of the JPEG image
 (in other words, the width will not be considered when determining the
 scaled image size.)</dd><dd><code>desiredHeight</code> - desired height (in pixels) of the decompressed image.
 Setting this to 0 is the same as setting it to the height of the JPEG
 image (in other words, the height will not be considered when determining
 the scaled image size.)</dd>
<dt><span class="strong">Returns:</span></dt><dd>the width of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</dd></dl>
</li>
</ul>
<a name="getScaledHeight(int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaledHeight</h4>
<pre>public&nbsp;int&nbsp;getScaledHeight(int&nbsp;desiredWidth,
                  int&nbsp;desiredHeight)</pre>
<div class="block">Returns the height of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>desiredWidth</code> - desired width (in pixels) of the decompressed image.
 Setting this to 0 is the same as setting it to the width of the JPEG image
 (in other words, the width will not be considered when determining the
 scaled image size.)</dd><dd><code>desiredHeight</code> - desired height (in pixels) of the decompressed image.
 Setting this to 0 is the same as setting it to the height of the JPEG
 image (in other words, the height will not be considered when determining
 the scaled image size.)</dd>
<dt><span class="strong">Returns:</span></dt><dd>the height of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</dd></dl>
</li>
</ul>
<a name="decompress(byte[], int, int, int, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompress</h4>
<pre>public&nbsp;void&nbsp;decompress(byte[]&nbsp;dstBuf,
              int&nbsp;x,
              int&nbsp;y,
              int&nbsp;desiredWidth,
              int&nbsp;pitch,
              int&nbsp;desiredHeight,
              int&nbsp;pixelFormat,
              int&nbsp;flags)
                throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a grayscale, RGB, or CMYK image
 to the given destination buffer.
 <p>
 NOTE: The output image is fully recoverable if this method throws a
 non-fatal <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><code>TJException</code></a> (unless
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_STOPONWARNING"><code>TJ.FLAG_STOPONWARNING</code></a> is specified.)</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>dstBuf</code> - buffer that will receive the decompressed/decoded image.
 If the source image is a JPEG image, then this buffer should normally be
 <code>pitch * scaledHeight</code> bytes in size, where
 <code>scaledHeight</code> can be determined by calling <code>
 scalingFactor.<a href="../../../org/libjpegturbo/turbojpeg/TJScalingFactor.html#getScaled(int)"><code>getScaled</code></a>(jpegHeight)
 </code> with one of the scaling factors returned from <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#getScalingFactors()"><code>TJ.getScalingFactors()</code></a> or by calling <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledHeight(int,%20int)"><code>getScaledHeight(int, int)</code></a>.  If the
 source image is a YUV image, then this buffer should normally be
 <code>pitch * height</code> bytes in size, where <code>height</code> is
 the height of the YUV image.  However, the buffer may also be larger than
 the dimensions of the source image, in which case the <code>x</code>,
 <code>y</code>, and <code>pitch</code> parameters can be used to specify
 the region into which the source image should be decompressed/decoded.</dd><dd><code>x</code> - x offset (in pixels) of the region in the destination image into
 which the source image should be decompressed/decoded</dd><dd><code>y</code> - y offset (in pixels) of the region in the destination image into
 which the source image should be decompressed/decoded</dd><dd><code>desiredWidth</code> - If the source image is a JPEG image, then this
 specifies the desired width (in pixels) of the decompressed image (or
 image region.)  If the desired destination image dimensions are different
 than the source image dimensions, then TurboJPEG will use scaling in the
 JPEG decompressor to generate the largest possible image that will fit
 within the desired dimensions.  Setting this to 0 is the same as setting
 it to the width of the JPEG image (in other words, the width will not be
 considered when determining the scaled image size.)  This parameter is
 ignored if the source image is a YUV image.</dd><dd><code>pitch</code> - bytes per line of the destination image.  Normally, this
 should be set to <code>scaledWidth * TJ.pixelSize(pixelFormat)</code> if
 the destination image is unpadded, but you can use this to, for instance,
 pad each line of the destination image to a 4-byte boundary or to
 decompress/decode the source image into a region of a larger image.  NOTE:
 if the source image is a JPEG image, then <code>scaledWidth</code> can be
 determined by calling <code>
 scalingFactor.<a href="../../../org/libjpegturbo/turbojpeg/TJScalingFactor.html#getScaled(int)"><code>getScaled</code></a>(jpegWidth)
 </code> or by calling <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledWidth(int,%20int)"><code>getScaledWidth(int, int)</code></a>.  If the source image is a
 YUV image, then <code>scaledWidth</code> is the width of the YUV image.
 Setting this parameter to 0 is the equivalent of setting it to
 <code>scaledWidth * TJ.pixelSize(pixelFormat)</code>.</dd><dd><code>desiredHeight</code> - If the source image is a JPEG image, then this
 specifies the desired height (in pixels) of the decompressed image (or
 image region.)  If the desired destination image dimensions are different
 than the source image dimensions, then TurboJPEG will use scaling in the
 JPEG decompressor to generate the largest possible image that will fit
 within the desired dimensions.  Setting this to 0 is the same as setting
 it to the height of the JPEG image (in other words, the height will not be
 considered when determining the scaled image size.)  This parameter is
 ignored if the source image is a YUV image.</dd><dd><code>pixelFormat</code> - pixel format of the decompressed/decoded image (one of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#PF_RGB"><code>TJ.PF_*</code></a>)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompress(byte[], int, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompress</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;decompress(byte[]&nbsp;dstBuf,
                         int&nbsp;desiredWidth,
                         int&nbsp;pitch,
                         int&nbsp;desiredHeight,
                         int&nbsp;pixelFormat,
                         int&nbsp;flags)
                throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompress(int, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompress</h4>
<pre>public&nbsp;byte[]&nbsp;decompress(int&nbsp;desiredWidth,
                int&nbsp;pitch,
                int&nbsp;desiredHeight,
                int&nbsp;pixelFormat,
                int&nbsp;flags)
                  throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance and return a buffer containing the decompressed image.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>desiredWidth</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a>
 for description</dd><dd><code>pitch</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a>
 for description</dd><dd><code>desiredHeight</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a>
 for description</dd><dd><code>pixelFormat</code> - pixel format of the decompressed image (one of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#PF_RGB"><code>TJ.PF_*</code></a>)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a buffer containing the decompressed image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompressToYUV</h4>
<pre>public&nbsp;void&nbsp;decompressToYUV(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;dstImage,
                   int&nbsp;flags)
                     throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a YUV planar image and store it in the given
 <code>YUVImage</code> instance.  This method performs JPEG decompression
 but leaves out the color conversion step, so a planar YUV image is
 generated instead of an RGB or grayscale image.  This method cannot be
 used to decompress JPEG source images with the CMYK or YCCK colorspace.
 <p>
 NOTE: The YUV planar output image is fully recoverable if this method
 throws a non-fatal <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><code>TJException</code></a> (unless
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_STOPONWARNING"><code>TJ.FLAG_STOPONWARNING</code></a> is specified.)</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>dstImage</code> - <a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg"><code>YUVImage</code></a> instance that will receive the YUV planar
 image.  The level of subsampling specified in this <code>YUVImage</code>
 instance must match that of the JPEG image, and the width and height
 specified in the <code>YUVImage</code> instance must match one of the
 scaled image sizes that TurboJPEG is capable of generating from the JPEG
 source image.</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompressToYUV(byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompressToYUV</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;decompressToYUV(byte[]&nbsp;dstBuf,
                              int&nbsp;flags)
                     throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>decompressToYUV(YUVImage, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompressToYUV(int, int[], int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompressToYUV</h4>
<pre>public&nbsp;<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;decompressToYUV(int&nbsp;desiredWidth,
                       int[]&nbsp;strides,
                       int&nbsp;desiredHeight,
                       int&nbsp;flags)
                         throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a set of Y, U (Cb), and V (Cr) image planes and return a
 <code>YUVImage</code> instance containing the decompressed image planes.
 This method performs JPEG decompression but leaves out the color
 conversion step, so a planar YUV image is generated instead of an RGB or
 grayscale image.  This method cannot be used to decompress JPEG source
 images with the CMYK or YCCK colorspace.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>desiredWidth</code> - desired width (in pixels) of the YUV image.  If the
 desired image dimensions are different than the dimensions of the JPEG
 image being decompressed, then TurboJPEG will use scaling in the JPEG
 decompressor to generate the largest possible image that will fit within
 the desired dimensions.  Setting this to 0 is the same as setting it to
 the width of the JPEG image (in other words, the width will not be
 considered when determining the scaled image size.)</dd><dd><code>strides</code> - an array of integers, each specifying the number of bytes
 per line in the corresponding plane of the output image.  Setting the
 stride for any plane to 0 is the same as setting it to the scaled
 component width of the plane.  If <tt>strides</tt> is NULL, then the
 strides for all planes will be set to their respective scaled component
 widths.  You can adjust the strides in order to add an arbitrary amount of
 line padding to each plane.</dd><dd><code>desiredHeight</code> - desired height (in pixels) of the YUV image.  If the
 desired image dimensions are different than the dimensions of the JPEG
 image being decompressed, then TurboJPEG will use scaling in the JPEG
 decompressor to generate the largest possible image that will fit within
 the desired dimensions.  Setting this to 0 is the same as setting it to
 the height of the JPEG image (in other words, the height will not be
 considered when determining the scaled image size.)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a YUV planar image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompressToYUV(int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompressToYUV</h4>
<pre>public&nbsp;<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;decompressToYUV(int&nbsp;desiredWidth,
                       int&nbsp;pad,
                       int&nbsp;desiredHeight,
                       int&nbsp;flags)
                         throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a unified YUV planar image buffer and return a
 <code>YUVImage</code> instance containing the decompressed image.  This
 method performs JPEG decompression but leaves out the color conversion
 step, so a planar YUV image is generated instead of an RGB or grayscale
 image.  This method cannot be used to decompress JPEG source images with
 the CMYK or YCCK colorspace.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>desiredWidth</code> - desired width (in pixels) of the YUV image.  If the
 desired image dimensions are different than the dimensions of the JPEG
 image being decompressed, then TurboJPEG will use scaling in the JPEG
 decompressor to generate the largest possible image that will fit within
 the desired dimensions.  Setting this to 0 is the same as setting it to
 the width of the JPEG image (in other words, the width will not be
 considered when determining the scaled image size.)</dd><dd><code>pad</code> - the width of each line in each plane of the YUV image will be
 padded to the nearest multiple of this number of bytes (must be a power of
 2.)</dd><dd><code>desiredHeight</code> - desired height (in pixels) of the YUV image.  If the
 desired image dimensions are different than the dimensions of the JPEG
 image being decompressed, then TurboJPEG will use scaling in the JPEG
 decompressor to generate the largest possible image that will fit within
 the desired dimensions.  Setting this to 0 is the same as setting it to
 the height of the JPEG image (in other words, the height will not be
 considered when determining the scaled image size.)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a YUV planar image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompressToYUV(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompressToYUV</h4>
<pre>@Deprecated
public&nbsp;byte[]&nbsp;decompressToYUV(int&nbsp;flags)
                       throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int,%20int,%20int)"><code>decompressToYUV(int, int, int, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompress(int[], int, int, int, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompress</h4>
<pre>public&nbsp;void&nbsp;decompress(int[]&nbsp;dstBuf,
              int&nbsp;x,
              int&nbsp;y,
              int&nbsp;desiredWidth,
              int&nbsp;stride,
              int&nbsp;desiredHeight,
              int&nbsp;pixelFormat,
              int&nbsp;flags)
                throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a grayscale, RGB, or CMYK image
 to the given destination buffer.
 <p>
 NOTE: The output image is fully recoverable if this method throws a
 non-fatal <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><code>TJException</code></a> (unless
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_STOPONWARNING"><code>TJ.FLAG_STOPONWARNING</code></a> is specified.)</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>dstBuf</code> - buffer that will receive the decompressed/decoded image.
 If the source image is a JPEG image, then this buffer should normally be
 <code>stride * scaledHeight</code> pixels in size, where
 <code>scaledHeight</code> can be determined by calling <code>
 scalingFactor.<a href="../../../org/libjpegturbo/turbojpeg/TJScalingFactor.html#getScaled(int)"><code>getScaled</code></a>(jpegHeight)
 </code> with one of the scaling factors returned from <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#getScalingFactors()"><code>TJ.getScalingFactors()</code></a> or by calling <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledHeight(int,%20int)"><code>getScaledHeight(int, int)</code></a>.  If the
 source image is a YUV image, then this buffer should normally be
 <code>stride * height</code> pixels in size, where <code>height</code> is
 the height of the YUV image.  However, the buffer may also be larger than
 the dimensions of the JPEG image, in which case the <code>x</code>,
 <code>y</code>, and <code>stride</code> parameters can be used to specify
 the region into which the source image should be decompressed.</dd><dd><code>x</code> - x offset (in pixels) of the region in the destination image into
 which the source image should be decompressed/decoded</dd><dd><code>y</code> - y offset (in pixels) of the region in the destination image into
 which the source image should be decompressed/decoded</dd><dd><code>desiredWidth</code> - If the source image is a JPEG image, then this
 specifies the desired width (in pixels) of the decompressed image (or
 image region.)  If the desired destination image dimensions are different
 than the source image dimensions, then TurboJPEG will use scaling in the
 JPEG decompressor to generate the largest possible image that will fit
 within the desired dimensions.  Setting this to 0 is the same as setting
 it to the width of the JPEG image (in other words, the width will not be
 considered when determining the scaled image size.)  This parameter is
 ignored if the source image is a YUV image.</dd><dd><code>stride</code> - pixels per line of the destination image.  Normally, this
 should be set to <code>scaledWidth</code>, but you can use this to, for
 instance, decompress the JPEG image into a region of a larger image.
 NOTE: if the source image is a JPEG image, then <code>scaledWidth</code>
 can be determined by calling <code>
 scalingFactor.<a href="../../../org/libjpegturbo/turbojpeg/TJScalingFactor.html#getScaled(int)"><code>getScaled</code></a>(jpegWidth)
 </code> or by calling <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledWidth(int,%20int)"><code>getScaledWidth(int, int)</code></a>.  If the source image is a
 YUV image, then <code>scaledWidth</code> is the width of the YUV image.
 Setting this parameter to 0 is the equivalent of setting it to
 <code>scaledWidth</code>.</dd><dd><code>desiredHeight</code> - If the source image is a JPEG image, then this
 specifies the desired height (in pixels) of the decompressed image (or
 image region.)  If the desired destination image dimensions are different
 than the source image dimensions, then TurboJPEG will use scaling in the
 JPEG decompressor to generate the largest possible image that will fit
 within the desired dimensions.  Setting this to 0 is the same as setting
 it to the height of the JPEG image (in other words, the height will not be
 considered when determining the scaled image size.)  This parameter is
 ignored if the source image is a YUV image.</dd><dd><code>pixelFormat</code> - pixel format of the decompressed image (one of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#PF_RGB"><code>TJ.PF_*</code></a>)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompress(java.awt.image.BufferedImage, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompress</h4>
<pre>public&nbsp;void&nbsp;decompress(java.awt.image.BufferedImage&nbsp;dstImage,
              int&nbsp;flags)
                throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a decompressed/decoded image to
 the given <code>BufferedImage</code> instance.
 <p>
 NOTE: The output image is fully recoverable if this method throws a
 non-fatal <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><code>TJException</code></a> (unless
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_STOPONWARNING"><code>TJ.FLAG_STOPONWARNING</code></a> is specified.)</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>dstImage</code> - a <code>BufferedImage</code> instance that will receive
 the decompressed/decoded image.  If the source image is a JPEG image, then
 the width and height of the <code>BufferedImage</code> instance must match
 one of the scaled image sizes that TurboJPEG is capable of generating from
 the JPEG image.  If the source image is a YUV image, then the width and
 height of the <code>BufferedImage</code> instance must match the width and
 height of the YUV image.</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="decompress(int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decompress</h4>
<pre>public&nbsp;java.awt.image.BufferedImage&nbsp;decompress(int&nbsp;desiredWidth,
                                      int&nbsp;desiredHeight,
                                      int&nbsp;bufferedImageType,
                                      int&nbsp;flags)
                                        throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and return a <code>BufferedImage</code>
 instance containing the decompressed/decoded image.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>desiredWidth</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a> for
 description</dd><dd><code>desiredHeight</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>decompress(byte[], int, int, int, int, int, int, int)</code></a> for
 description</dd><dd><code>bufferedImageType</code> - the image type of the <code>BufferedImage</code>
 instance that will be created (for instance,
 <code>BufferedImage.TYPE_INT_RGB</code>)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a <code>BufferedImage</code> instance containing the
 decompressed/decoded image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="close()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()
           throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Free the native structures associated with this decompressor instance.</div>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>close</code>&nbsp;in interface&nbsp;<code>java.io.Closeable</code></dd>
<dt><strong>Specified by:</strong></dt>
<dd><code>close</code>&nbsp;in interface&nbsp;<code>java.lang.AutoCloseable</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="finalize()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>finalize</h4>
<pre>protected&nbsp;void&nbsp;finalize()
                 throws java.lang.Throwable</pre>
<dl>
<dt><strong>Overrides:</strong></dt>
<dd><code>finalize</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>java.lang.Throwable</code></dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/libjpegturbo/turbojpeg/TJDecompressor.html" target="_top">Frames</a></li>
<li><a href="TJDecompressor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
