EXPORTS
  jcopy_block_row @ 1 ;
  jcopy_sample_rows @ 2 ;
  jdiv_round_up @ 3 ;
  jinit_1pass_quantizer @ 4 ;
  jinit_2pass_quantizer @ 5 ;
  jinit_c_coef_controller @ 6 ;
  jinit_c_main_controller @ 7 ;
  jinit_c_master_control @ 8 ;
  jinit_c_prep_controller @ 9 ;
  jinit_color_converter @ 10 ;
  jinit_color_deconverter @ 11 ;
  jinit_compress_master @ 12 ;
  jinit_d_coef_controller @ 13 ;
  jinit_d_main_controller @ 14 ;
  jinit_d_post_controller @ 15 ;
  jinit_downsampler @ 16 ;
  jinit_forward_dct @ 17 ;
  jinit_huff_decoder @ 18 ;
  jinit_huff_encoder @ 19 ;
  jinit_input_controller @ 20 ;
  jinit_inverse_dct @ 21 ;
  jinit_marker_reader @ 22 ;
  jinit_marker_writer @ 23 ;
  jinit_master_decompress @ 24 ;
  jinit_memory_mgr @ 25 ;
  jinit_merged_upsampler @ 26 ;
  jinit_phuff_decoder @ 27 ;
  jinit_phuff_encoder @ 28 ;
  jinit_upsampler @ 29 ;
  jpeg_CreateCompress @ 30 ;
  jpeg_CreateDecompress @ 31 ;
  jpeg_abort @ 32 ;
  jpeg_abort_compress @ 33 ;
  jpeg_abort_decompress @ 34 ;
  jpeg_add_quant_table @ 35 ;
  jpeg_alloc_huff_table @ 36 ;
  jpeg_alloc_quant_table @ 37 ;
  jpeg_calc_jpeg_dimensions @ 38 ;
  jpeg_calc_output_dimensions @ 39 ;
  jpeg_consume_input @ 40 ;
  jpeg_copy_critical_parameters @ 41 ;
  jpeg_core_output_dimensions @ 42 ;
  jpeg_default_colorspace @ 43 ;
  jpeg_default_qtables @ 44 ;
  jpeg_destroy @ 45 ;
  jpeg_destroy_compress @ 46 ;
  jpeg_destroy_decompress @ 47 ;
  jpeg_fdct_float @ 48 ;
  jpeg_fdct_ifast @ 49 ;
  jpeg_fdct_islow @ 50 ;
  jpeg_fill_bit_buffer @ 51 ;
  jpeg_finish_compress @ 52 ;
  jpeg_finish_decompress @ 53 ;
  jpeg_finish_output @ 54 ;
  jpeg_free_large @ 55 ;
  jpeg_free_small @ 56 ;
  jpeg_gen_optimal_table @ 57 ;
  jpeg_get_large @ 58 ;
  jpeg_get_small @ 59 ;
  jpeg_has_multiple_scans @ 60 ;
  jpeg_huff_decode @ 61 ;
  jpeg_idct_1x1 @ 62 ;
  jpeg_idct_2x2 @ 63 ;
  jpeg_idct_4x4 @ 64 ;
  jpeg_idct_float @ 65 ;
  jpeg_idct_ifast @ 66 ;
  jpeg_idct_islow @ 67 ;
  jpeg_input_complete @ 68 ;
  jpeg_make_c_derived_tbl @ 69 ;
  jpeg_make_d_derived_tbl @ 70 ;
  jpeg_mem_available @ 71 ;
  jpeg_mem_dest @ 72 ;
  jpeg_mem_init @ 73 ;
  jpeg_mem_src @ 74 ;
  jpeg_mem_term @ 75 ;
  jpeg_new_colormap @ 76 ;
  jpeg_open_backing_store @ 77 ;
  jpeg_quality_scaling @ 78 ;
  jpeg_read_coefficients @ 79 ;
  jpeg_read_header @ 80 ;
  jpeg_read_raw_data @ 81 ;
  jpeg_read_scanlines @ 82 ;
  jpeg_resync_to_restart @ 83 ;
  jpeg_save_markers @ 84 ;
  jpeg_set_colorspace @ 85 ;
  jpeg_set_defaults @ 86 ;
  jpeg_set_linear_quality @ 87 ;
  jpeg_set_marker_processor @ 88 ;
  jpeg_set_quality @ 89 ;
  jpeg_simple_progression @ 90 ;
  jpeg_start_compress @ 91 ;
  jpeg_start_decompress @ 92 ;
  jpeg_start_output @ 93 ;
  jpeg_std_error @ 94 ;
  jpeg_stdio_dest @ 95 ;
  jpeg_stdio_src @ 96 ;
  jpeg_suppress_tables @ 97 ;
  jpeg_write_coefficients @ 98 ;
  jpeg_write_m_byte @ 99 ;
  jpeg_write_m_header @ 100 ;
  jpeg_write_marker @ 101 ;
  jpeg_write_raw_data @ 102 ;
  jpeg_write_scanlines @ 103 ;
  jpeg_write_tables @ 104 ;
  jround_up @ 105 ;
  jzero_far @ 106 ;
  jpeg_skip_scanlines @ 107 ;
  jpeg_crop_scanline @ 108 ;
  jpeg_read_icc_profile @ 109 ;
  jpeg_write_icc_profile @ 110 ;
