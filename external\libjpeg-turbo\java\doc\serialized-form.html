<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Serialized Form</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Serialized Form";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="org/libjpegturbo/turbojpeg/package-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<div class="serializedFormContainer">
<ul class="blockList">
<li class="blockList">
<h2 title="Package">Package&nbsp;org.libjpegturbo.turbojpeg</h2>
<ul class="blockList">
<li class="blockList"><a name="org.libjpegturbo.turbojpeg.TJException">
<!--   -->
</a>
<h3>Class <a href="org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">org.libjpegturbo.turbojpeg.TJException</a> extends java.io.IOException implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList"><a name="serializedForm">
<!--   -->
</a>
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>errorCode</h4>
<pre>int errorCode</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="org.libjpegturbo.turbojpeg.TJTransform">
<!--   -->
</a>
<h3>Class <a href="org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">org.libjpegturbo.turbojpeg.TJTransform</a> extends java.awt.Rectangle implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-127367705761430371L</dd>
</dl>
<ul class="blockList">
<li class="blockList"><a name="serializedForm">
<!--   -->
</a>
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>op</h4>
<pre>int op</pre>
<div class="block">Transform operation (one of <code>OP_*</code>)</div>
</li>
<li class="blockList">
<h4>options</h4>
<pre>int options</pre>
<div class="block">Transform options (bitwise OR of one or more of <code>OPT_*</code>)</div>
</li>
<li class="blockListLast">
<h4>cf</h4>
<pre><a href="org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a> cf</pre>
<div class="block">Custom filter instance</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="org/libjpegturbo/turbojpeg/package-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
