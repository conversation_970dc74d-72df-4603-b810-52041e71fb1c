/**
 * @file alarm.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include <stdio.h>
#include "lv_watch.h"

#if USE_LV_WATCH_ALARM

/*********************
 *      DEFINES
 *********************/
#define SECDAY              86400L                                           // seconds per day
#define ALARM_TEST  0

#if defined(__XF_WS_VENDOR_CT_ZHJ__)
#define ALARM_RERING_CNT    6  // reRing counts ,default is 3
#define ALARM_RERING_TIME   600  // 600 seconds to rering alarm
#elif USE_LV_WATCH_ALARM_UI_CY!=0
#define ALARM_RERING_CNT    6  // reRing counts ,default is 6
#define ALARM_RERING_TIME   600  // 600 seconds to rering alarm
#else
#if USE_LV_WATCH_CT!=0 || USE_LV_WATCH_WS_LEEFINE!=0 || USE_LV_WATCH_SETTING_POWERON_OFF!=0
#define ALARM_RERING_CNT    0  // reRing counts ,eirc set 0 20220914
#else
#define ALARM_RERING_CNT    3  // reRing counts ,default is 3
#endif
#define ALARM_RERING_TIME   300  // 300 seconds to rering alarm
#endif

static app_adaptor_alarm_t alarms[NV_ALARM_MAX_ALARM_NUM];
static int8_t alarm_recent_index = -1;  // recent alarm index
static app_adaptor_alarm_ind alarm_ind_func = NULL;
static uint8_t alarm_on_flag = 0;  //  alarm on flag
static bool is_poweroff_alarm = false; 
static bool is_poweroff_alarm_waiting = false; 
static int alarm_rering_cnt = 0;
static void *   mmi_rering_alarm_Timer = NULL;
static int8_t alarm_last_index = -1;
#if USE_LV_WATCH_ALARM_S2C_NAME!=0
static char alarm_alert_curname[30+1];
static int8_t alarm_rering_index = -1;  // rering alarm index
#endif
#if 1 //USE_WATCH_FACE_UNLOCK != 0
static bool alarm_is_ringing = false;
#endif

#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
alarm_repeat_t alarm_repeat[NV_ALARM_MAX_ALARM_NUM]={0};
#define CYCLE_SECONDS  ALARM_RERING_TIME
#define CYCLE   ALARM_RERING_CNT
static void *   mmi_repeat_alarm_Timer = NULL;
static void alarm_rering_ring(int alarm_type);
void update_repeat_alarm_timer();
int alarm_ringten_time=0;
#endif

uint32_t alarms_id_next_alarm_ring = 0;
uint32_t alarms_id_repeat_ring = 0;
#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
holiday_i_t gAlarmHoliday[ALARM_MAX_HOLIDAYS_NUM];
#endif
#if USE_LV_WATCH_ALARM_UI_CY !=0
TIMER_ID ALARM_TIME = NULL;
#endif

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void poweron_by_alarm(void);
static void alarm_prepare_destory(lv_obj_t * activity_obj);
static lv_alarm_obj_ext_t * alarm_get_ext(void);
static void alarm_ring(int alarm_type);
void alarm_set_alarm(app_adaptor_alarm_t * alarm, uint8_t count);
void alarm_set_alarm_ext(app_adaptor_alarm_t * alarm);
static void alarm_set_recent_to_rtc(app_adaptor_alarm_t * alarms);
void alarm_audio_ctrl_callback(AUDIO_CTRL_PRIORITY priority);
static void alarm_rering_req();
static void poweroff_alarm_rering_req();
static void alarm_timeout(void * para);
static void alarm_prepare_destory_ex(lv_obj_t * activity_obj);
void alarm_timeout_cleanup(void);
void alarm_repeat_ring(int alarm_type);

/**********************
 *   GLOBAL FUNCTIONS
***********************/

static void action_ten_min_ring(lv_obj_t * btn, lv_event_t e)
{
	if(LV_EVENT_CLICKED != e) {
		return;
	}

	alarm_ringten_time = 1;
	alarm_timeout_cleanup();
}

void key_close_alarm()
{
	ws_printf("key_close_alarm  ");
	alarm_ringten_time = 1;
	alarm_timeout_cleanup();
}
static void action_close(lv_obj_t * btn, lv_event_t e)
{
	if(LV_EVENT_CLICKED != e) {
		return;
	}

	printf("[DBUG_TEST] action_close: 用户点击停止按钮\n");
	printf("[DBUG_TEST] action_close: alarm_recent_index=%d, alarm_last_index=%d\n", alarm_recent_index, alarm_last_index);

	// 检查当前响铃的闹钟状态
	if(alarm_recent_index >= 0 && alarm_recent_index < NV_ALARM_MAX_ALARM_NUM) {
		printf("[DBUG_TEST] action_close: 当前响铃闹钟[%d] - valid=%d, on_off=%d, repeat_bitmap=0x%02x\n",
			alarm_recent_index, alarms[alarm_recent_index].valid, alarms[alarm_recent_index].on_off, alarms[alarm_recent_index].repeat_bitmap);
		printf("[DBUG_TEST] action_close: 是否为一次性闹钟: %s\n",
			(0 == (alarms[alarm_recent_index].repeat_bitmap & 0x80)) ? "是" : "否");
	}

	if(alarm_last_index >= 0 && alarm_last_index < NV_ALARM_MAX_ALARM_NUM) {
		printf("[DBUG_TEST] action_close: 最后响铃闹钟[%d] - valid=%d, on_off=%d, repeat_bitmap=0x%02x\n",
			alarm_last_index, alarms[alarm_last_index].valid, alarms[alarm_last_index].on_off, alarms[alarm_last_index].repeat_bitmap);
		printf("[DBUG_TEST] action_close: 是否为一次性闹钟: %s\n",
			(0 == (alarms[alarm_last_index].repeat_bitmap & 0x80)) ? "是" : "否");
	}

	printf("[DBUG_TEST] action_close: is_poweroff_alarm=%d\n", is_poweroff_alarm);

	alarm_cleanup();

}

bool Get_alarm_ring()
{
	return alarm_is_ringing;
}

lv_obj_t * alarm_create(lv_obj_t * activity_obj)
{
    /* activity obj */
    if(NULL == activity_obj) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_ALARM;
        activity_ext.create = alarm_create;
        activity_ext.prepare_destory = alarm_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    }

    /* watch obj */
    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    lv_watch_obj_set_anim_mode(obj, LV_WATCH_ANIM_HOR_RIGHT_HIDE);
    lv_alarm_obj_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_alarm_obj_ext_t));
	ext->gif_info = NULL;
#if USE_LV_WATCH_ANMO !=0
	if(get_anmo_status()){
		Hal_Vibrator_Play_End();
	}
#endif


#if 1 //USE_WATCH_FACE_UNLOCK != 0
	alarm_is_ringing = true;
#endif


    lv_obj_t * label = lv_label_create(obj, NULL);
    hal_rtc_t rtc_curr;            // current rtc time
    char time_info[30];
    Hal_Rtc_Gettime(&rtc_curr);
	printf("Alarm ring time = %d:%02d:%02d", rtc_curr.tm_hour, rtc_curr.tm_min, rtc_curr.tm_sec);
    if(rtc_curr.tm_sec>50){
        seconds_to_time(10+time_to_seconds(&rtc_curr), &rtc_curr);
    }
#if USE_LV_WATCH_HOUR_SYSTEM != 0
	if (hour_system_get_time_format()){
		if(rtc_curr.tm_hour>12){
			sprintf(time_info, "%.2d:%.2d pm", rtc_curr.tm_hour-12, rtc_curr.tm_min);
		}else{
			sprintf(time_info, "%.2d:%.2d am", rtc_curr.tm_hour, rtc_curr.tm_min);
		}
	}else
#endif
	{
		sprintf(time_info, "%.2d:%.2d", rtc_curr.tm_hour, rtc_curr.tm_min);
	}

	
    lv_label_set_text(label, time_info);
    lv_obj_set_width(label, LV_HOR_RES-20);
    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
	lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font40);
    lv_obj_set_click(label, false);
	lv_obj_align(label, NULL, LV_ALIGN_IN_TOP_MID, 0, 138);


#if USE_LV_WATCH_ALARM_S2C_NAME!=0
if(strlen(alarm_alert_curname)>0){
    lv_obj_t * label_name = lv_label_create(obj, NULL);
    lv_label_set_text(label_name, alarm_alert_curname);
    lv_obj_set_width(label_name, LV_HOR_RES-4);
    lv_label_set_align(label_name, LV_LABEL_ALIGN_CENTER);
	#if defined(__XF_LCD_SIZE_240X280__)
    lv_obj_align(label_name, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -30);
	#else
    lv_obj_align(label_name, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -2);
	#endif
    lv_obj_add_style(label_name, LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_obj_set_click(label_name, false);
}
#endif

	lv_obj_t* alarm_img = lv_img_create(obj, NULL);
	lv_obj_set_click(alarm_img , false);
	lv_img_set_src(alarm_img, ICON_ALARM_RING);
	lv_obj_align(alarm_img, obj, LV_ALIGN_IN_TOP_MID, 0, 40);


	lv_obj_t * ten_mints_btn = lv_btn_create(obj, NULL);
	lv_obj_set_size(ten_mints_btn, 100,52);
	lv_obj_set_click(ten_mints_btn,true);
	lv_obj_align(ten_mints_btn,obj,LV_ALIGN_IN_TOP_LEFT,12,196);
    lv_obj_set_style_local_bg_color(ten_mints_btn, LV_BTN_PART_MAIN, LV_STATE_DEFAULT,  LV_COLOR_MAKE(0x40,0x40,0x40));
    lv_obj_set_style_local_bg_opa(ten_mints_btn, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, 255);
    lv_obj_set_style_local_radius(ten_mints_btn, LV_BTN_PART_MAIN,  LV_STATE_DEFAULT, 34);
	lv_obj_t * ten_mints_label = lv_label_create(ten_mints_btn, NULL);
	#if !defined(__XF_PRO_ENGLISH__)
	lv_label_set_text(ten_mints_label," 稍后 ");
	lv_obj_add_style(ten_mints_label, LV_LABEL_PART_MAIN, &lv_watch_font30);
	#else
	lv_label_set_text(ten_mints_label," Later ");
	lv_obj_add_style(ten_mints_label, LV_LABEL_PART_MAIN, &lv_watch_font20);
	#endif

	lv_obj_align(ten_mints_label,ten_mints_btn,LV_ALIGN_CENTER,0,0);
	lv_obj_set_event_cb(ten_mints_btn, action_ten_min_ring);	


	lv_obj_t * close_btn = lv_btn_create(obj, ten_mints_btn);
	lv_obj_set_style_local_bg_color(close_btn, LV_BTN_PART_MAIN, LV_STATE_DEFAULT,  LV_COLOR_MAKE(0x01,0x85,0xff));
	lv_obj_align(close_btn,obj,LV_ALIGN_IN_TOP_LEFT,128,196);
	lv_obj_t * close_label = lv_label_create(close_btn, NULL);
	#if !defined(__XF_PRO_ENGLISH__)
	lv_label_set_text(close_label," 停止 ");
	lv_obj_add_style(close_label, LV_LABEL_PART_MAIN, &lv_watch_font30);
	#else
	lv_label_set_text(close_label," CLOSE ");
	lv_obj_add_style(close_label, LV_LABEL_PART_MAIN, &lv_watch_font20);
	#endif

	lv_obj_align(close_label,close_btn,LV_ALIGN_CENTER,0,0);
	lv_obj_set_event_cb(close_btn, action_close);

    /* start timer (1 min) */
    ext->timer_id = Hal_Timer_Start(1000 * 50, alarm_timeout, NULL, 0);
    ext->timer_running = true;

    watch_set_suspend_enable(false, ACT_ID_ALARM, 0);

    return obj;
}

uint8_t * alarm_get_valid(int num)
{
    return alarms[num].valid;
}

void alarm_read_nv()
{
	nv_watch_alarm_t nv_alarm;	
	Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));	
	UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);
	for(int i=0;i<NV_ALARM_MAX_ALARM_NUM;i++)
	{
		alarms[i].valid = nv_alarm.alarm_info[i].valid;
		alarms[i].on_off = nv_alarm.alarm_info[i].on_off;
		alarms[i].hour = nv_alarm.alarm_info[i].hour;
		alarms[i].min = nv_alarm.alarm_info[i].min;
		alarms[i].repeat_bitmap = nv_alarm.alarm_info[i].repeat_bitmap;
#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
		alarms[i].type= nv_alarm.alarm_info[i].type;
#endif
#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
		alarms[i].ring_en=nv_alarm.alarm_info[i].ring_en ; 
		alarms[i].vib_en=nv_alarm.alarm_info[i].vib_en ;			
#endif
	#if USE_LV_WATCH_ALARM_S2C_NAME!=0
		strncpy(alarms[i].name,nv_alarm.alarm_info[i].name,30);
	#endif

#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
		alarms[i].realert_delay=nv_alarm.alarm_info[i].realert_delay; 
		alarms[i].is_skip_holiday=nv_alarm.alarm_info[i].is_skip_holiday ;
		strncpy(alarms[i].repeat_str,nv_alarm.alarm_info[i].repeat_str,15);
		strncpy(alarms[i].namegbk,nv_alarm.alarm_info[i].namegbk,NV_ALARM_TOKEN_LEN_MAX*2-1);
#endif
	}
}

void alarm_init(void)
{
    Hal_Mem_Set(alarms, 0, sizeof(app_adaptor_alarm_t) * NV_ALARM_MAX_ALARM_NUM);
    alarm_recent_index = -1;

	#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN !=0
	alarm_repeat_Infom_init();
	#endif

#if ALARM_TEST
    app_adaptor_alarm_t app_alarm[NV_ALARM_MAX_ALARM_NUM];
    Hal_Mem_Set(app_alarm, 0, sizeof(app_adaptor_alarm_t) * NV_ALARM_MAX_ALARM_NUM);

    app_alarm[0].valid = 1;
    app_alarm[0].on_off = 1;
    app_alarm[0].hour = 17;
    app_alarm[0].min = 20;
    app_alarm[0].repeat_bitmap = 1;

    app_alarm[1].valid = 1;
    app_alarm[1].on_off = 0;
    app_alarm[1].hour = 15;
    app_alarm[1].min = 13;
    app_alarm[1].repeat_bitmap = 1;

    alarm_set_alarm(app_alarm, 2);

    Hal_Timer_Start(1000 * 5, (CB_FUNC)alarm_ring, NULL, 0);
    //Hal_Timer_Start(1000 * 5, (CB_FUNC)shutdown, NULL, 0);
#else
    nv_watch_alarm_t nv_alarm;
    Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));
    /* read NVM */
    UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);
    for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
        alarms[i].valid = nv_alarm.alarm_info[i].valid;
        alarms[i].on_off = nv_alarm.alarm_info[i].on_off;
        alarms[i].hour = nv_alarm.alarm_info[i].hour;
        alarms[i].min = nv_alarm.alarm_info[i].min;		
		alarms[i].alarm_id = nv_alarm.alarm_info[i].alarm_id;
        alarms[i].repeat_bitmap = nv_alarm.alarm_info[i].repeat_bitmap;
		#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
	 	alarms[i].type= nv_alarm.alarm_info[i].type;
		#endif
		#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
		alarms[i].ring_en=nv_alarm.alarm_info[i].ring_en ; 
		alarms[i].vib_en=nv_alarm.alarm_info[i].vib_en ;			
		#endif
		#if USE_LV_WATCH_ALARM_S2C_NAME!=0
		strncpy(alarms[i].name,nv_alarm.alarm_info[i].name,30);
		#endif

	#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
		alarms[i].realert_delay=nv_alarm.alarm_info[i].realert_delay; 
		alarms[i].is_skip_holiday=nv_alarm.alarm_info[i].is_skip_holiday ;
		strncpy(alarms[i].repeat_str,nv_alarm.alarm_info[i].repeat_str,15);
		strncpy(alarms[i].namegbk,nv_alarm.alarm_info[i].namegbk,NV_ALARM_TOKEN_LEN_MAX*2-1);
	#endif
		
		
    }
    
    if(alarm_last_index < 0){
        alarm_last_index = nv_alarm.alarm_last_index;
    }
	
    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
#endif

    /* bind set alarm function */
    app_adaptor_set_alarm_bind(alarm_set_alarm);
}

#if USE_LV_WATCH_ALARM_LIST_LOCAL_EDIT!=0
void alarm_set_alarm_index(uint8_t cur_id,app_adaptor_alarm_t * data)
{
	alarms[cur_id].valid = data->valid;
	alarms[cur_id].hour = data->hour;
	alarms[cur_id].min = data->min;
	alarms[cur_id].repeat_bitmap = data->repeat_bitmap;
    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}

void alarm_set_alarm_deleteall(void)
{
    Hal_Mem_Set(alarms, 0, sizeof(app_adaptor_alarm_t) * NV_ALARM_MAX_ALARM_NUM);
    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}

#else
void alarm_set_alarm_deleteall(void)
{
    nv_watch_alarm_t nv_alarm;
    Hal_Mem_Set(alarms, 0, sizeof(app_adaptor_alarm_t) * NV_ALARM_MAX_ALARM_NUM);
	
	memset(&nv_alarm,0,sizeof(nv_watch_alarm_t));
    UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);
    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}

#endif
void alarm_cleanup(void)
{
    ws_printf("alarm_cleanup---in");
	printf("[DBUG_TEST] alarm_cleanup: 开始清理闹钟\n");
	printf("[DBUG_TEST] alarm_cleanup: alarm_recent_index=%d, alarm_last_index=%d\n", alarm_recent_index, alarm_last_index);
	printf("[DBUG_TEST] alarm_cleanup: is_poweroff_alarm=%d\n", is_poweroff_alarm);

	/* del obj */
	lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);

	if(activity_obj) {
		printf("[DBUG_TEST] alarm_cleanup: 找到闹钟UI对象，准备销毁\n");
		alarm_prepare_destory_ex(activity_obj);
		lv_obj_del(activity_obj);
		printf("[DBUG_TEST] alarm_cleanup: 闹钟UI对象已删除\n");
	} else {
		printf("[DBUG_TEST] alarm_cleanup: 未找到闹钟UI对象\n");
	}

	printf("[DBUG_TEST] alarm_cleanup: 清理完成，但未更新闹钟状态\n");
}

int alarm_id_ringed()
{
	return alarms_id_repeat_ring;
}
void alarm_timeout_cleanup(void)
{
    ws_printf("alarm_timeout_cleanup---in");

    /* del obj */
    lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
    if(activity_obj) {
		lv_watch_png_cache_all_free();
	
		/* stop playing tone */
		Hal_Tone_Play_End();
	#if USE_LV_WATCH_ALARM_ALERT_VIB!=0
    	Hal_Vibrator_Play_End();
	#endif
	
		lv_alarm_obj_ext_t * ext = alarm_get_ext();
		if(ext) {
#if	USE_LV_WATCH_ALARM_UI_CY!=1

			/* gif close */
			if(ext->gif_info) {
				gif_close(ext->gif_info);
				ext->gif_info = NULL;
			}
#endif
			/* stop timer */
			if(ext->timer_running && ext->timer_id) {
				Hal_Timer_Stop(ext->timer_id);
			}

		}

		Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_3);

		watch_set_suspend_enable(true, ACT_ID_ALARM, 0);

        lv_obj_del(activity_obj);
		
		alarm_rering_cnt ++;

		ws_printf("alarm_timeout_cleanup alarm_rering_cnt:%d\n",alarm_rering_cnt);
		#if USE_LV_WATCH_ALARM_UI_CY !=0	
		alarm_list_update_ui();
		#endif
#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN !=0

		update_repeat_alarm_timer();
		
		if(alarm_rering_cnt < ALARM_RERING_CNT)
		{
			if(mmi_rering_alarm_Timer==NULL)		 
				uos_timer_create(&mmi_rering_alarm_Timer);
			
			if(is_poweroff_alarm)
			{
				uos_timer_start(mmi_rering_alarm_Timer, (ALARM_RERING_TIME-50)*200, 0, poweroff_alarm_rering_req, 1);
				is_poweroff_alarm_waiting = true;
			}
			else	
			    stop_alarm_rering_timer();
		}
		else
			stop_alarm_rering_timer();
#else
		if(alarm_rering_cnt < ALARM_RERING_CNT){
			
			if(mmi_rering_alarm_Timer==NULL){			 
				uos_timer_create(&mmi_rering_alarm_Timer);
			}
			if(is_poweroff_alarm){
				uos_timer_start(mmi_rering_alarm_Timer, (ALARM_RERING_TIME-50)*200, 0, poweroff_alarm_rering_req, 1);
				is_poweroff_alarm_waiting = true;
			}
			else
				uos_timer_start(mmi_rering_alarm_Timer, (ALARM_RERING_TIME-50)*200, 0, alarm_rering_req, 1);
		}
		else{	
			stop_alarm_rering_timer();
			
			if(is_poweroff_alarm)
				power_off_alarm_off();
		}
#endif
	}
#ifndef USE_WATCH_LITE	
	watch_thirdparty_resume(THIRDPARTY_PAUSE_ALARM);
#endif

	alarm_is_ringing = false;

#if USE_WATCH_FACE_UNLOCK != 0
	do_faceunlock_flag();
#endif
}

void alarm_shutdown(hal_rtc_t * poweron_time)
{
    /* set poweron by alarm */
    if(poweron_time) {
        Hal_Rtc_Set_Alarm(RTC_ALARM_1, poweron_time, poweron_by_alarm);
        Hal_Rtc_Enable_Alarm(RTC_ALARM_1, true);
    } else {
        Hal_Rtc_Enable_Alarm(RTC_ALARM_1, false);
    }

    /* shutdown */
    shutdown_confirm_btn_action(NULL, LV_EVENT_CLICKED);
}

void alarm_set_alarm_by_ui(app_adaptor_alarm_t * alarm, uint8_t count)
{
    alarm_set_alarm(alarm, count);

    //indicate alarm to APP
    if(alarm_ind_func) {
        alarm_ind_func(alarm, count);
    }
}

app_adaptor_alarm_t * alarm_get_alarm(void)
{
    return alarms;
}

void app_adaptor_alarm_ind_bind(app_adaptor_alarm_ind func)
{
    alarm_ind_func = func;
}

void alarm_set_rtc_alarm(void)
{
    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}

/**********************
*   STATIC FUNCTIONS
**********************/
static void poweron_by_alarm(void)
{
    printf("poweron_by_alarm\n");
}

static void power_off_alarm_off(void)
{
    Hal_Backlight_Off();
    watch_set_ready_state(false);
    watch_set_suspend_enable(false,ACT_ID_ALARM, 0);
    Hal_Power_Off(HAL_TYPE_POWER_OFF);
}

static void alarm_prepare_destory(lv_obj_t * activity_obj)
{
    lv_watch_png_cache_all_free();

    /* stop playing tone */
    Hal_Tone_Play_End();
#if USE_LV_WATCH_ALARM_ALERT_VIB!=0
	Hal_Vibrator_Play_End();
#endif

    lv_alarm_obj_ext_t * ext = alarm_get_ext();
    if(ext) {
        /* stop timer */
        if(ext->timer_running && ext->timer_id) {
            Hal_Timer_Stop(ext->timer_id);
        }
    }
	
#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
	int8_t alarm_close_id = -1;
	alarm_close_id = get_current_max_alarm_seconds_id();
	ws_printf("alarm_prepare_destory() alarm_close_id = %d",alarm_close_id);
	if(alarm_close_id>=0&&alarm_close_id<NV_ALARM_MAX_ALARM_NUM)
	{
		alarm_repeat[alarm_close_id].Alarm_Repeat_Seconds = 0;
		alarm_repeat[alarm_close_id].Alarm_Repeat_Cycle = 0;
		//UPclocktime_repeat(alarm_close_id);
		alarm_close_id = -1;
	}
	update_repeat_alarm_timer();
#else
	stop_alarm_rering_timer();
#endif
	
	alarm_rering_cnt = 0;
	
	if(is_poweroff_alarm)
	{
		power_off_alarm_off();
	}

    Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_3);
#if 0
    /* inform voice msg of the end */
    voice_msg_alarm_end();
#endif /* USE_LV_WATCH_VOICE_MSG */
    watch_set_suspend_enable(true,ACT_ID_ALARM, 0);

#ifndef USE_WATCH_LITE	
	watch_thirdparty_resume(THIRDPARTY_PAUSE_ALARM);
#endif

	alarm_is_ringing = false;

#if USE_WATCH_FACE_UNLOCK != 0
	do_faceunlock_flag();
#endif

}

static void alarm_prepare_destory_ex(lv_obj_t * activity_obj)
{
    lv_watch_png_cache_all_free();

	ws_printf("alarm_prepare_destory_ex---in");
	printf("[DBUG_TEST] alarm_prepare_destory_ex: 开始销毁闹钟UI\n");
	printf("[DBUG_TEST] alarm_prepare_destory_ex: alarm_recent_index=%d, alarm_last_index=%d\n", alarm_recent_index, alarm_last_index);
	printf("[DBUG_TEST] alarm_prepare_destory_ex: is_poweroff_alarm=%d\n", is_poweroff_alarm);

    /* stop playing tone */
    Hal_Tone_Play_End();
#if USE_LV_WATCH_ALARM_ALERT_VIB!=0
	Hal_Vibrator_Play_End();
#endif
    lv_alarm_obj_ext_t * ext = alarm_get_ext();
    if(ext) {
#if USE_LV_WATCH_ALARM_UI_CY!=1
        /* gif close */
        if(ext->gif_info) {
            gif_close(ext->gif_info);
            ext->gif_info = NULL;
        }
#endif
        /* stop timer */
        if(ext->timer_running && ext->timer_id) {
            Hal_Timer_Stop(ext->timer_id);
        }
    }
	
#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
	int8_t alarm_close_id = -1;
	alarm_close_id = get_current_max_alarm_seconds_id();
	ws_printf("alarm_prepare_destory_ex() alarm_close_id = %d",alarm_close_id);
	if(alarm_close_id>=0&&alarm_close_id<NV_ALARM_MAX_ALARM_NUM)
	{
		alarm_repeat[alarm_close_id].Alarm_Repeat_Seconds = 0;
		alarm_repeat[alarm_close_id].Alarm_Repeat_Cycle = 0;
		//UPclocktime_repeat(alarm_close_id);
		alarm_close_id = -1;
	}
	update_repeat_alarm_timer();
#else
	stop_alarm_rering_timer();
#endif
	
#if USE_LV_WATCH_ALARM_UI_CY !=0	
	alarm_list_update_ui();
#endif
	alarm_rering_cnt = 0;
	
	if(is_poweroff_alarm)
	{
		power_off_alarm_off();
	}

    Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_3);

    watch_set_suspend_enable(true,ACT_ID_ALARM, 0);
#ifndef USE_WATCH_LITE	
	watch_thirdparty_resume(THIRDPARTY_PAUSE_ALARM);
#endif
}


static lv_alarm_obj_ext_t * alarm_get_ext(void)
{
    lv_obj_t * activity_obj = NULL;
    lv_obj_t * watch_obj = NULL;
    lv_alarm_obj_ext_t  * ext = NULL;

    activity_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
    if(activity_obj) {
        lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &watch_obj);
    }

    if(watch_obj) {
        ext = lv_obj_get_ext_attr(watch_obj);
    }
    return ext;
}

static void alarm_timeout(void * para)
{
    (void) para;
	ws_printf("alarm_timeout---in");
	lv_alarm_obj_ext_t * ext = alarm_get_ext();
	if(ext) {
		ext->timer_running = false;
		ext->timer_id = NULL;
		ws_printf("alarm_timeout--- set timer_id==null"); 
	}

    alarm_timeout_cleanup();
}

void alarm_audio_ctrl_callback(AUDIO_CTRL_PRIORITY priority)
{
    printf("%s,priority is %d\n", __FUNCTION__,priority);
    if(AUDIO_CTRL_PRIORITY_3 == priority)
    {
        lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
        if(activity_obj != NULL) return;

        /* play tone start */
        if(!wsMuteTime_CheckNow(NULL,NULL)){
			#if USE_LV_WATCH_ALLVOL_SET != 0
			Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
			#else
	        Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
			#endif
			#if USE_LV_WATCH_ALARM_ALERT_VIB!=0
			Hal_Vibrator_Play_Repeat();
			#endif
        }
        //watch_wakeup_lcd(true);
		Wakeup_GuiTask(true);
        /* display alarm animation */
        alarm_create(NULL);
    }
    else if(AUDIO_CTRL_PRIORITY_3 > priority)
    {
        // clean up alarm if any
        alarm_cleanup();
    }
}

#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
static void alarm_rering_req()
{
	MMI_ModemAdp_Rpc_Req(alarm_rering_ring, NV_ALARM_MAX_ALARM_NUM, 0, 0);
}
#else
static void alarm_rering_req()
{
	MMI_ModemAdp_Rpc_Req(alarm_repeat_ring, NV_ALARM_MAX_ALARM_NUM, 0, 0);
}
#endif

static void poweroff_alarm_rering_req()
{
	MMI_ModemAdp_Rpc_Req(poweroff_alarm_ring, NV_ALARM_MAX_ALARM_NUM, 0, 0);
}

#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
int8_t get_current_min_alarm_seconds_id() //获取重复响铃闹钟中时间最小的闹钟id
{	
	int8_t current_id = -1;
	uint32_t min_alarm_time = 0x7FFFFFFF;
	for(uint8_t i=0; i<NV_ALARM_MAX_ALARM_NUM;i++)
	{	
	    ws_printf("get_min_alarm---alarm_repeat[%d].Alarm_Repeat_Seconds=%d",i,alarm_repeat[i].Alarm_Repeat_Seconds);
		if(alarm_repeat[i].Alarm_Repeat_Seconds !=0 )
		{
			if(min_alarm_time>=alarm_repeat[i].Alarm_Repeat_Seconds)
			{
				min_alarm_time = alarm_repeat[i].Alarm_Repeat_Seconds;
				current_id = i;

			}	
		}
	}
	return current_id;
}
int8_t get_current_max_alarm_seconds_id()//获取重复响铃闹钟中时间最大的闹钟id
{	
	uint32_t max_alarm_time = 0;
	int8_t current_id = -1;
	for(uint8_t i=0; i<NV_ALARM_MAX_ALARM_NUM;i++)
	{	
	    ws_printf("get_max_alarm---alarm_repeat[%d].Alarm_Repeat_Seconds=%d",i,alarm_repeat[i].Alarm_Repeat_Seconds);
		if(alarm_repeat[i].Alarm_Repeat_Seconds !=0 )
		{
			if(max_alarm_time<=alarm_repeat[i].Alarm_Repeat_Seconds)
			{
				max_alarm_time = alarm_repeat[i].Alarm_Repeat_Seconds;
				current_id = i;
			}	
		}
	}

	return current_id;
}
void update_repeat_alarm_timer()
{
	uint32_t difference_time = 0;
	int8_t start_alarm_timer_id = -1;
	if(mmi_repeat_alarm_Timer==NULL)			 
		uos_timer_create(&mmi_repeat_alarm_Timer);
	
	start_alarm_timer_id = get_current_min_alarm_seconds_id();
	if(start_alarm_timer_id<0 || start_alarm_timer_id>NV_ALARM_MAX_ALARM_NUM)
		return;
	difference_time = alarm_repeat[start_alarm_timer_id].Alarm_Repeat_Seconds- (PMIC_RTC_GetTime_Count(0)-28800);
	if(difference_time!=0)
	{
	    ws_printf("update_repeat_alarm_timer--time=%d ",difference_time);
		uos_timer_stop(mmi_repeat_alarm_Timer);
		uos_timer_start(mmi_repeat_alarm_Timer,difference_time*TICKES_IN_SECOND,0, alarm_rering_req, 1);
	}

}

static void alarm_rering_ring(int alarm_type)
{
	bool ring_en=TRUE;
	bool vib_en=TRUE;
	int8_t alarm_running_id = -1;
	uint32_t min_alarm_time = 0x7FFFFFFF;

	ws_printf("alarm_rering_ring in---");
	
	alarm_running_id = get_current_min_alarm_seconds_id();
	if(alarm_running_id<0 || alarm_running_id>NV_ALARM_MAX_ALARM_NUM)
		return;
	
	alarm_repeat[alarm_running_id].Alarm_Repeat_Cycle--;
	if(alarm_repeat[alarm_running_id].Alarm_Repeat_Cycle==0)
	{
		alarm_repeat[alarm_running_id].Alarm_Repeat_Seconds = 0;
		//UPclocktime_repeat(alarm_running_id);
	}
	else
		alarm_repeat[alarm_running_id].Alarm_Repeat_Seconds = PMIC_RTC_GetTime_Count(0)-28800+CYCLE_SECONDS;

    ws_printf("alarm_rering_ring,alarm_repeat[%d].Alarm_Repeat_Seconds=%d",alarm_running_id,alarm_repeat[alarm_running_id].Alarm_Repeat_Seconds);
	
	nv_watch_alarm_t nv_alarm;
	Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));
	/* read NVM */
	UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);


#if (USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0)||(USE_LV_WATCH_DISPLAY_PLAT_ALARM!=0)
		if(nv_alarm.alarm_info[alarm_running_id].ring_en==0)
				ring_en=FALSE;
		if(nv_alarm.alarm_info[alarm_running_id].vib_en==0)
				vib_en=FALSE;
#endif


	ws_printf("ring_enring_enring_enring_en2222=%d",ring_en);

#if USE_LV_WATCH_ALARM_S2C_NAME != 0
	memset(alarm_alert_curname,0,sizeof(alarm_alert_curname));
	if(alarm_running_id >= 0)
	{
		if(strlen(alarms[alarm_running_id].name)>0)
		{
			strncpy(alarm_alert_curname,alarms[alarm_running_id].name,strlen(alarms[alarm_running_id].name));
		}
	}
#endif

	if(!watch_is_ready()){
		Hal_Rtc_Enable_Alarm(RTC_ALARM_1, false);
		return;
	}

	lv_watch_png_cache_all_free();

	lv_obj_t * alarm_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
	if(NULL != alarm_obj) {
		return;
	}

	if(!watch_is_ready()) {
		watch_set_alarm_flag(true);
		return;
	}
	
	is_poweroff_alarm = false;

	ws_printf("ring_enring_enring_enring_en=%d",ring_en);
	if(AUDIO_CTRL_PRIORITY_3 == Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_3,alarm_audio_ctrl_callback))
	{
		if(!wsMuteTime_CheckNow(NULL,NULL)){
			if(ring_en)
			{
				#if USE_LV_WATCH_ALLVOL_SET != 0
				Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
				#else
	        	Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
				#endif
			}
		#if USE_LV_WATCH_ALARM_ALERT_VIB!=0
			if(vib_en)
			{
			
				Hal_Vibrator_Play_Repeat();
			}
		#endif
		}

		Wakeup_GuiTask(true);
		/* display alarm animation */
		alarm_create(NULL);
	}	
}

uint_8 get_alarm_runing_status(int8_t alrarm_index)
{	
	if(alarm_repeat[alrarm_index].Alarm_Repeat_Cycle !=0)
	{
		return 1;	
	}
	return 0;
}
void alarm_repeat_Infom_init()
{
	memset(alarm_repeat,0,sizeof(alarm_repeat));
}
#endif


void alarm_repeat_ring(int alarm_type)
{
	bool ring_en=TRUE;
	bool vib_en=TRUE;

	nv_watch_alarm_t nv_alarm;
	Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));
	/* read NVM */
	UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);

#if (USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0)||(USE_LV_WATCH_DISPLAY_PLAT_ALARM!=0)
		if(nv_alarm.alarm_info[alarm_recent_index_bk].ring_en==0)
				ring_en=FALSE;
		if(nv_alarm.alarm_info[alarm_recent_index_bk].vib_en==0)
				vib_en=FALSE;
#endif

#if USE_LV_WATCH_ALARM_S2C_NAME != 0
	memset(alarm_alert_curname,0,sizeof(alarm_alert_curname));
	if(alarm_recent_index_bk >= 0)
	{
		if(strlen(alarms[alarm_recent_index_bk].name)>0)
		{
			strncpy(alarm_alert_curname,alarms[alarm_recent_index_bk].name,strlen(alarms[alarm_recent_index_bk].name));
		}
	}
#endif

	if(!watch_is_ready()){
		Hal_Rtc_Enable_Alarm(RTC_ALARM_1, false);
		return;
	}

	lv_watch_png_cache_all_free();

	lv_obj_t * alarm_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
	if(NULL != alarm_obj) {
		return;
	}

	if(!watch_is_ready()) {
		watch_set_alarm_flag(true);
		return;
	}

	ws_printf("ring_enring_enring_enring_en=%d",ring_en);
	if(AUDIO_CTRL_PRIORITY_3 == Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_3,alarm_audio_ctrl_callback))
	{
		/* play tone start */
    #if 1//USE_LV_WATCH_MUTETIME_ALARM != 0
		if(!wsMuteTime_CheckNow(NULL,NULL)){
			if(ring_en)
			{
				#if USE_LV_WATCH_ALLVOL_SET != 0
				Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
				#else
	        	Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
				#endif
			}
		#if USE_LV_WATCH_ALARM_ALERT_VIB!=0
			if(vib_en)
			{
			
				Hal_Vibrator_Play_Repeat();
			}
		#endif
		}
    #else     
		Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
    #endif


		Wakeup_GuiTask(true);
		/* display alarm animation */
		alarm_create(NULL);
	}	
}
#if USE_LV_WATCH_ALARM_UI_CY !=0

void alarm_close_phone_dial()
{
	Hal_Timer_Stop(ALARM_TIME);
	ALARM_TIME = NULL;
	key_close_alarm();
}
#endif
static void alarm_ring(int alarm_type)
{
	alarm_rering_cnt = 0;
	bool ring_en=TRUE;
#if USE_LV_WATCH_ALARM_ALERT_VIB!=0 || USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
	bool vib_en=TRUE;
#endif
#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
	bool is_notalert_inHoliday=false;
#endif
    bool alarm_changed = false;

	ws_printf("alarm_ring, alarm_type=%d alarm_recent_index=%d\n",alarm_type,alarm_recent_index);

#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
	if((alarm_recent_index<NV_ALARM_MAX_ALARM_NUM) && (alarm_recent_index>=0))
	{
		alarm_repeat[alarm_recent_index].Alarm_Repeat_Seconds = PMIC_RTC_GetTime_Count(0)-28800+CYCLE_SECONDS;
		alarm_repeat[alarm_recent_index].Alarm_Repeat_Cycle= CYCLE;
	}
#endif

#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
	bool act_is_to_poweroff=false;
	bool act_is_to_poweron=false;
	bool act_in_standby_is_type_poweron=false;
	bool act_in_poweroff_chg_is_type_poweroff=false;
	
	if(alarm_type!=NV_ALARM_MAX_ALARM_NUM)
	{
	 /*not rering alarm*/
		if(alarm_type_is_shecdule_poweroff())
		{
			if(is_in_poweroff_chg_mode()){
				act_in_poweroff_chg_is_type_poweroff=true;
				printf("alarm_ring, type is schedule poweroff alarm ,in poweroff chg\n");
			}else{
				act_is_to_poweroff=true;
				printf("alarm_ring, type is schedule poweroff alarm ,need to poeroff\n");
			}
		}
		else
		{
			if(alarm_recent_index >= 0){
				if(alarms[alarm_recent_index].type ==ALARM_TYPE_POWERON)
				{
					if(is_in_poweroff_chg_mode()){
						act_is_to_poweron=true;
						printf("alarm_ring, poweroff_chg_mode,so act_is_to_poweron");
					}
					else{
						act_in_standby_is_type_poweron= true;
						printf("alarm_ring, act_in_standby_is_type_poweronn");
					}
				}
			}

		}
	}
#endif

#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
if(alarm_recent_index >= 0)
{
	if(alarms[alarm_recent_index].ring_en==0)
			ring_en=FALSE;
	if(alarms[alarm_recent_index].vib_en==0)
			vib_en=FALSE;
}
#endif

#if USE_LV_WATCH_ALARM_S2C_NAME != 0
	memset(alarm_alert_curname,0,sizeof(alarm_alert_curname));
    if(alarm_recent_index >= 0)
	{
		if(strlen(alarms[alarm_recent_index].name)>0){
			strncpy(alarm_alert_curname,alarms[alarm_recent_index].name,30);
		}
	}
	
	if(alarm_type==NV_ALARM_MAX_ALARM_NUM)
	{
	/*delay ring*/
		if(strlen(alarms[alarm_rering_index].name)>0){
			memset(alarm_alert_curname,0,sizeof(alarm_alert_curname));
			strncpy(alarm_alert_curname,alarms[alarm_rering_index].name,30);
		}
	}
	else
	{
		alarm_rering_index=alarm_recent_index; /*normal ring*/

	}
#endif
#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
    if(alarm_recent_index >= 0)
    {
		if(alarms[alarm_recent_index].is_skip_holiday)
		{
			printf("alarm_ring, here chech is skip holidy\n");
			if(alarm_is_in_holiday()){
				is_notalert_inHoliday=true;
				#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
				ring_en=FALSE;
				vib_en=FALSE;
				#endif
			}
		}
	}
#endif

	//printf("alarm_ring, alarm_type=%d alarm_recent_index=%d\n",alarm_type,alarm_recent_index);
	printf("[DBUG_TEST] alarm_ring: 开始处理闹钟响铃，alarm_type=%d, alarm_recent_index=%d\n", alarm_type, alarm_recent_index);
    if(alarm_recent_index >= 0) {
        printf("[DBUG_TEST] alarm_ring: 准备处理一次性闹钟状态更新\n");
        /* set alarm off */
        for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
            if(alarm_recent_index == i) {
                printf("[DBUG_TEST] alarm_ring: 检查当前响铃闹钟[%d] - repeat_bitmap=0x%02x, on_off=%d\n",
                    i, alarms[i].repeat_bitmap, alarms[i].on_off);
                if((0 == (alarms[i].repeat_bitmap & 0x80)) && (alarm_type!=NV_ALARM_MAX_ALARM_NUM)) {  // alarm once
                    printf("[DBUG_TEST] alarm_ring: 闹钟[%d]是一次性闹钟，设置为关闭状态\n", i);
                    alarms[i].on_off = 0;
                    alarm_changed = true;
                } else {
                    printf("[DBUG_TEST] alarm_ring: 闹钟[%d]不是一次性闹钟或是重复响铃，保持状态不变\n", i);
                }
            } else {
                if((alarms[i].hour == alarms[alarm_recent_index].hour)
                        && (alarms[i].min == alarms[alarm_recent_index].min)
                        && ((0 == (alarms[i].repeat_bitmap & 0x80)) && (alarm_type!=NV_ALARM_MAX_ALARM_NUM)) // alarm once
                        && (1 == alarms[i].on_off)) {
                    printf("[DBUG_TEST] alarm_ring: 找到相同时间的一次性闹钟[%d]，设置为关闭状态\n", i);
                    alarms[i].on_off = 0;
                    alarm_changed = true;
                }
            }
        }

        if(alarm_changed) {
            /* write to NVM */
            nv_watch_alarm_t nv_alarm;
            for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
                nv_alarm.alarm_info[i].valid = alarms[i].valid;
                nv_alarm.alarm_info[i].on_off = alarms[i].on_off;
                nv_alarm.alarm_info[i].hour = alarms[i].hour;
                nv_alarm.alarm_info[i].min = alarms[i].min;
				nv_alarm.alarm_info[i].alarm_id = alarms[i].alarm_id;
                nv_alarm.alarm_info[i].repeat_bitmap = alarms[i].repeat_bitmap;
				#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
				nv_alarm.alarm_info[i].type= alarms[i].type;
				#endif
				#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
				nv_alarm.alarm_info[i].ring_en = alarms[i].ring_en; 
				nv_alarm.alarm_info[i].vib_en = alarms[i].vib_en;			
				#endif
				#if USE_LV_WATCH_ALARM_S2C_NAME!=0
				strncpy(nv_alarm.alarm_info[i].name,alarms[i].name,30);
				#endif
				#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
				nv_alarm.alarm_info[i].realert_delay=alarms[i].realert_delay; 
				nv_alarm.alarm_info[i].is_skip_holiday=alarms[i].is_skip_holiday ;	
				strncpy(nv_alarm.alarm_info[i].repeat_str,alarms[i].repeat_str,15);
				strncpy(nv_alarm.alarm_info[i].namegbk,alarms[i].namegbk,NV_ALARM_TOKEN_LEN_MAX*2-1);
				#endif
				
            }

            UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);
			
			#if defined(__XF_WS_VENDOR_CT_ZHJ__)
			app_adaptor_ws_report_alarm_state(alarms);
			#endif

			
        }

        alarm_recent_index = -1;
    }

	/* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);

	if(!watch_is_ready()){
		Hal_Rtc_Enable_Alarm(RTC_ALARM_1, false);
		return;
	}
#if USE_LV_WATCH_UPGRADE != 0
	if(UGD_STATE_NULL != upgrade_get_state()){
		Hal_Rtc_Enable_Alarm(RTC_ALARM_1, false);
		return;
	}
#endif

#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
	if(act_in_standby_is_type_poweron)
	{
		printf("alarm_ring, act_in_standby_is_type_poweron,drop\n");
		return;
	}
	if(act_in_poweroff_chg_is_type_poweroff)
	{
		printf("alarm_ring, act_in_poweroff_chg_is_type_poweroff,drop\n");
		return;
	}
#endif

#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
	if(is_notalert_inHoliday)
	{
		printf("alarm_ring, is_notalert_inHoliday,drop\n");
		return;
	}
#endif

#ifdef USE_WATCH_LITE
	watch_thirdparty_exit();
#else
	watch_thirdparty_pause(THIRDPARTY_PAUSE_ALARM);
#endif

#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
	if(act_is_to_poweroff)
	{
		printf("alarm_ring, doing poweroff\n");
		set_alarm_make_schedule_poweronoff_silent(true);
		shutdown_confirm_btn_action(NULL,LV_EVENT_CLICKED);
		return;
	}
	if(act_is_to_poweron)
	{
		printf("alarm_ring, in poweroff charging to do power onf\n");
		board_power_off(HAL_TYPE_REBOOT);
		return;
	}
#endif

    lv_watch_png_cache_all_free();

    lv_obj_t * alarm_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
    if(NULL != alarm_obj) {
        return;
    }

    if(!watch_is_ready()) {
        watch_set_alarm_flag(true);
        return;
    }

	is_poweroff_alarm = false;
	
#if USE_LV_WATCH_ALARM_UI_CY !=0
	if(lv_watch_get_activity_obj(ACT_ID_PHONE))
	{
		Hal_Vibrator_Play_Onetime(NULL, 2000);
		Wakeup_GuiTask(true);
	    /* display alarm animation */
	    alarm_create(NULL);
		ALARM_TIME = Hal_Timer_Start(1000 * 2, alarm_close_phone_dial, NULL, 0);

	}
	else
	{
#endif
    if(AUDIO_CTRL_PRIORITY_3 == Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_3,alarm_audio_ctrl_callback))
    {
		
        lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
        if(activity_obj == NULL) {
	        /* play tone start */
	        #if 1//USE_LV_WATCH_MUTETIME_ALARM != 0
	        if(!wsMuteTime_CheckNow(NULL, NULL))
			{
				if(ring_en)
				{
					#if USE_LV_WATCH_ALLVOL_SET != 0
					Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
					#else
	        		Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
					#endif
				}
				#if USE_LV_WATCH_ALARM_ALERT_VIB!=0 || USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
				if(vib_en)
					Hal_Vibrator_Play_Repeat();
				#endif
	        }
	        #else     
	        Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
	        #endif

			Wakeup_GuiTask(true);
	        /* display alarm animation */
	        alarm_create(NULL);
        }
    }
#if USE_LV_WATCH_ALARM_UI_CY !=0	
	}
#endif

	if (alarm_type == 0){
		stop_alarm_rering_timer();
		alarm_rering_cnt = 0;
	}

#if USE_LV_WATCH_ALARM_SET
    if(alarm_changed) {
        /* delete alarm add */
        lv_obj_t * alarm_set_obj = lv_watch_get_activity_obj(ACT_ID_ALARM_ADD);
        if(alarm_set_obj) {
            lv_obj_del(alarm_set_obj);
        }

        /* delete alarm edit */
        alarm_set_obj = lv_watch_get_activity_obj(ACT_ID_ALARM_EDIT);
        if(alarm_set_obj) {
            lv_obj_del(alarm_set_obj);
        }

        /* recreate alarm list */
        alarm_set_obj = lv_watch_get_activity_obj(ACT_ID_ALARM_LIST);
        if(alarm_set_obj) {
            alarm_list_create(alarm_set_obj);
        }
    }
#endif

#if USE_LV_WATCH_ALARM_UI_CY !=1	
#if USE_LV_WATCH_ALARM_LIST != 0
    alarm_list_update_ui();
#endif
#endif
}

void poweroff_alarm_ring(int alarm_type)
{
	bool ring_en=TRUE;
	//bool vib_en=TRUE;
	printf("poweroff_alarm_ring, alarm_type=%d alarm_last_index=%d\n",alarm_type,alarm_last_index);
#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
		if(alarms[alarm_last_index].ring_en==0)
				ring_en=FALSE;
		//if(alarms[alarm_last_index].vib_en==0)
				//vib_en=FALSE;
#endif
#if USE_LV_WATCH_ALARM_S2C_NAME != 0
		memset(alarm_alert_curname,0,sizeof(alarm_alert_curname));
		if(alarm_last_index >= 0)
		{
			if(strlen(alarms[alarm_last_index].name)>0){
				strncpy(alarm_alert_curname,alarms[alarm_last_index].name,30);
			}
		}
#endif

    printf("[DBUG_TEST] poweroff_alarm_ring: alarm_type=%d, alarm_last_index=%d\n", alarm_type, alarm_last_index);
    if(alarm_type<NV_ALARM_MAX_ALARM_NUM && alarm_last_index >= 0) {
        printf("[DBUG_TEST] poweroff_alarm_ring: 检查关机闹钟[%d] - repeat_bitmap=0x%02x\n",
            alarm_last_index, alarms[alarm_last_index].repeat_bitmap);
        if(0 == (alarms[alarm_last_index].repeat_bitmap & 0x80)){  // alarm once
            printf("[DBUG_TEST] poweroff_alarm_ring: 关机闹钟[%d]是一次性闹钟，清空闹钟数据\n", alarm_last_index);
            /* set alarm off */
            Hal_Mem_Set(&alarms[alarm_last_index], 0, sizeof(app_adaptor_alarm_t));

            /* write to NVM */
            nv_watch_alarm_t nv_alarm;
            for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
                nv_alarm.alarm_info[i].valid = alarms[i].valid;
                nv_alarm.alarm_info[i].hour = alarms[i].hour;
                nv_alarm.alarm_info[i].min = alarms[i].min;
				nv_alarm.alarm_info[i].alarm_id = alarms[i].alarm_id;
                nv_alarm.alarm_info[i].repeat_bitmap = alarms[i].repeat_bitmap;
				#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
				nv_alarm.alarm_info[i].type= alarms[i].type;
				#endif
				#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
				nv_alarm.alarm_info[i].ring_en = alarms[i].ring_en; 
				nv_alarm.alarm_info[i].vib_en = alarms[i].vib_en;			
				#endif
				#if USE_LV_WATCH_ALARM_S2C_NAME!=0
				strncpy(nv_alarm.alarm_info[i].name,alarms[i].name,30);
				#endif
				#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
				nv_alarm.alarm_info[i].realert_delay=alarms[i].realert_delay; 
				nv_alarm.alarm_info[i].is_skip_holiday=alarms[i].is_skip_holiday ;		
				strncpy(nv_alarm.alarm_info[i].repeat_str,alarms[i].repeat_str,15);
				strncpy(nv_alarm.alarm_info[i].namegbk,alarms[i].namegbk,NV_ALARM_TOKEN_LEN_MAX*2-1);
				#endif

				
            }

            UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);
        }

        alarm_last_index = -1;
    }

    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);

    lv_obj_t * alarm_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
    if(NULL != alarm_obj) {
        return;
    }
	
	if (alarm_type == 0){
		stop_alarm_rering_timer();
		alarm_rering_cnt = 0;
	}
	
	AudioHAL_SetResBufCnt(20);

    // hal api
	if(ring_en)
	{
		#if USE_LV_WATCH_ALLVOL_SET != 0
    	Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
		#else
		Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
		#endif
	}
	is_poweroff_alarm = true;
	Wakeup_GuiTask(true);
    /* display alarm animation */
    alarm_create(NULL);
	is_poweroff_alarm_waiting = false;
}

void alarm_set_alarm_last_index(int8_t active_index)
{
    nv_watch_alarm_t nv_alarm;
    Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));
    /* read NVM */
    UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);

    nv_alarm.alarm_last_index = active_index;

    UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);
}
void alarm_set_alarm(app_adaptor_alarm_t * alarm, uint8_t count)
{
	#if USE_LV_WATCH_SETTING_POWERON_OFF !=0
	if(count < NV_ALARM_MAX_ALARM_NUM ){
    	Hal_Mem_Set(alarms, 0, sizeof(app_adaptor_alarm_t) *(NV_ALARM_MAX_ALARM_NUM-2));
	}
	#else
	printf("%s: alarm=%p, alarms=%p\n", __FUNCTION__,alarm, alarms);
	if(alarm!=alarms){
		/*external app set ,here clear to zero first,if is itset,not clear*/
    	Hal_Mem_Set(alarms, 0, sizeof(app_adaptor_alarm_t) * NV_ALARM_MAX_ALARM_NUM);
	}
	#endif

    for(uint8_t i = 0; i < count; i++) {
        Hal_Mem_Copy(&alarms[i], alarm + i, sizeof(app_adaptor_alarm_t));
    }

    /* write to NVM */
    nv_watch_alarm_t nv_alarm;
    Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));

    for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
        nv_alarm.alarm_info[i].valid = alarms[i].valid;
        nv_alarm.alarm_info[i].on_off = alarms[i].on_off;
        nv_alarm.alarm_info[i].hour = alarms[i].hour;
        nv_alarm.alarm_info[i].min = alarms[i].min;
		nv_alarm.alarm_info[i].alarm_id = alarms[i].alarm_id;
        nv_alarm.alarm_info[i].repeat_bitmap = alarms[i].repeat_bitmap;
		#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
		nv_alarm.alarm_info[i].type = alarms[i].type;
		#endif
		#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
		nv_alarm.alarm_info[i].ring_en = alarms[i].ring_en;	
		nv_alarm.alarm_info[i].vib_en = alarms[i].vib_en;			
		#endif
		#if USE_LV_WATCH_ALARM_S2C_NAME!=0
		strncpy(nv_alarm.alarm_info[i].name,alarms[i].name,30);
		#endif
		#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
		nv_alarm.alarm_info[i].realert_delay=alarms[i].realert_delay; 
		nv_alarm.alarm_info[i].is_skip_holiday=alarms[i].is_skip_holiday ;		
		strncpy(nv_alarm.alarm_info[i].repeat_str,alarms[i].repeat_str,15);
		strncpy(nv_alarm.alarm_info[i].namegbk,alarms[i].namegbk,NV_ALARM_TOKEN_LEN_MAX*2-1);
		#endif
    }

    UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);

    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}


#if USE_LV_WATCH_SINGLE_ALARM_LIST != 0
void alarm_set_alarm_ext(app_adaptor_alarm_t * alarm)
{
    /* write to NVM */
    nv_watch_alarm_t nv_alarm;
    uint8_t i;
    
    for(i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
        if(alarms[i].token[0] == 0){
            break;
        }        
        if(!strncmp(alarms[i].token, alarm->token, NV_ALARM_TOKEN_LEN_MAX)){
            break;
        }
    }

    if(i < NV_ALARM_MAX_ALARM_NUM && alarm->valid){
        alarms[i].valid = alarm->valid;
        alarms[i].on_off = alarm->on_off;
        alarms[i].hour = alarm->hour;
        alarms[i].min = alarm->min;
        alarms[i].repeat_bitmap = alarm->repeat_bitmap;
        strncpy(alarms[i].token, alarm->token, WATCH_ALARM_TOKEN_LEN_MAX);
        #if USE_LV_WATCH_ALARM_S2C_NAME!=0
        if(alarm->name[0] != 0xff || alarm->name[1] != 0xff){
            strncpy(alarms[i].name, alarm->name, 30);
        }
        #endif
    }

    for(i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
        nv_alarm.alarm_info[i].valid = alarms[i].valid;
        nv_alarm.alarm_info[i].on_off = alarms[i].on_off;
        nv_alarm.alarm_info[i].hour = alarms[i].hour;
        nv_alarm.alarm_info[i].min = alarms[i].min;
		nv_alarm.alarm_info[i].alarm_id = alarms[i].alarm_id;
        nv_alarm.alarm_info[i].repeat_bitmap = alarms[i].repeat_bitmap;
        memcpy(nv_alarm.alarm_info[i].token, alarms[i].token, NV_ALARM_TOKEN_LEN_MAX);
        #if USE_LV_WATCH_ALARM_S2C_NAME!=0
        strncpy(nv_alarm.alarm_info[i].name, alarms[i].name, 30);
        #endif
    }

    UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);

    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}

void alarm_delete_alarm(char *token)
{
    /* write to NVM */
    nv_watch_alarm_t nv_alarm;
    uint8_t i;
    
    for(i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
        if(!strncmp(alarms[i].token, token, NV_ALARM_TOKEN_LEN_MAX)){
            if( i < NV_ALARM_MAX_ALARM_NUM - 1 ){
                for(++i; i < NV_ALARM_MAX_ALARM_NUM; i++){
                    memcpy(&alarms[i-1], &alarms[i], sizeof(app_adaptor_alarm_t));
                }
            }
            memset(&alarms[NV_ALARM_MAX_ALARM_NUM-1], 0, sizeof(app_adaptor_alarm_t));
            break;
        }
    }

    UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);

    /* set recent alarm to rtc */
    alarm_set_recent_to_rtc(alarms);
}


#endif

#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
uint8_t alarm_in_poweroff_type_is_shecdule(void)
{
	printf("alarm_in_poweroff_type_is_shecdule alarm_last_index:%d",alarm_last_index);
	uint8_t type=ALARM_TYPE_NORMAL;
	
	if(alarm_last_index >= 0)
	{
		if((alarms[alarm_last_index].type ==ALARM_TYPE_POWERON)||(alarms[alarm_last_index].type ==ALARM_TYPE_POWEROFF))
		{
				type=alarms[alarm_last_index].type;
				printf("alarm_in_poweroff_type_is_shecdule is ALARM_TYPE_POWERON/OFF:%d",type);

	        	if(0 == (alarms[alarm_last_index].repeat_bitmap & 0x80)){  // alarm once
		            /* set alarm off */
		            Hal_Mem_Set(&alarms[alarm_last_index], 0, sizeof(app_adaptor_alarm_t));

		            /* write to NVM */
		            nv_watch_alarm_t nv_alarm;
		            for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
		                nv_alarm.alarm_info[i].valid = alarms[i].valid;
		                nv_alarm.alarm_info[i].hour = alarms[i].hour;
		                nv_alarm.alarm_info[i].min = alarms[i].min;
						nv_alarm.alarm_info[i].alarm_id = alarms[i].alarm_id;
		                nv_alarm.alarm_info[i].repeat_bitmap = alarms[i].repeat_bitmap;
						#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
						nv_alarm.alarm_info[i].type= alarms[i].type;
						#endif
		            }

		            UI_NV_Write_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (uint8_t *)&nv_alarm);

        			alarm_last_index = -1;
    			}

		    /* set recent alarm to rtc */
		    alarm_set_recent_to_rtc(alarms);
			
			return type;
		}
	}
	
	return type;
}

bool alarm_in_poweroff_type_is_shecdule_poweroff(void)
{
	printf("alarm_type_is_shecdule_poweroff-alarm_recent_index:%d",alarm_recent_index);
	if(alarm_recent_index >= 0){
		printf("alarm_type_is_shecdule_poweroff-type:%d",alarms[alarm_recent_index].type);
		if(alarms[alarm_recent_index].type ==ALARM_TYPE_POWEROFF)
			return true;
	}
	
	return false;
}


bool alarm_type_is_shecdule_poweroff(void)
{
	printf("alarm_type_is_shecdule_poweroff-alarm_recent_index:%d",alarm_recent_index);
	if(alarm_recent_index >= 0){
		printf("alarm_type_is_shecdule_poweroff-type:%d",alarms[alarm_recent_index].type);
		if(alarms[alarm_recent_index].type ==ALARM_TYPE_POWEROFF)
			return true;
	}
	
	return false;
}

#endif
/**
 * Set recent alarm to rtc
 * param (in) alarms: app_adaptor_alarm_t *
 * return  VOID
 */
static void alarm_set_recent_to_rtc(app_adaptor_alarm_t * alarm)
{
    hal_rtc_t rtc_curr;            // current rtc time
    hal_rtc_t rtc_alarm;           // alarm rtc time
    hal_rtc_t rtc_recent;          // recent alarm rtc time
    uint32_t  second_curr   = 0;   // seconds of current time
    uint32_t  second_recent = 0;   // seconds of recent alarm
    uint32_t  second_tmp    = 0;
    uint32_t  second_alarm[NV_ALARM_MAX_ALARM_NUM];   // second of alarms
    bool has_alarm_on = false;

    Hal_Mem_Set(second_alarm, 0, sizeof(uint32_t) * NV_ALARM_MAX_ALARM_NUM);

    Hal_Rtc_Gettime(&rtc_curr);
    second_curr = time_to_seconds(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

    /* get recent alarm */
    for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
        if((1 == alarm[i].valid) && (1 == alarm[i].on_off)) {
            has_alarm_on = true;
            Hal_Mem_Set(&rtc_alarm, 0, sizeof(hal_rtc_t));
            rtc_alarm.tm_min  = alarm[i].min;
            rtc_alarm.tm_hour = alarm[i].hour;
            rtc_alarm.tm_mday = rtc_curr.tm_mday;
            rtc_alarm.tm_mon  = rtc_curr.tm_mon;
            rtc_alarm.tm_year = rtc_curr.tm_year;

            if(0 == (alarm[i].repeat_bitmap & 0x80)) {  // alarm once
                second_alarm[i] = time_to_seconds(&rtc_alarm);
                if(second_alarm[i] <= second_curr) {
                    second_alarm[i] += SECDAY; // add one day
                }
            } else { // alarm repeat
                second_alarm[i]    = time_to_seconds(&rtc_alarm);
                second_recent = 0;

                for(uint8_t j = 0; j < 7; j++) {
                    if(0x01 == ((alarm[i].repeat_bitmap >> j) & 0x01)) {
                        second_tmp = second_alarm[i] + ((j - rtc_curr.tm_wday + 7) % 7) * SECDAY;
                        if(second_tmp <= second_curr) {
                            second_tmp += SECDAY * 7; // add one week
                        }

                        if((0 == second_recent) || (second_tmp < second_recent)) {
                            second_recent = second_tmp;
                        }
                    }
                }
                second_alarm[i] = second_recent;
            }
        }
    }

    if(has_alarm_on) {
        second_recent = 0;
        for(uint8_t i = 0; i < NV_ALARM_MAX_ALARM_NUM; i++) {
            if((second_alarm[i] > 0) && ((0 == second_recent) || (second_alarm[i] < second_recent))) {
                second_recent = second_alarm[i];
                alarm_recent_index = i;
				alarms_id_next_alarm_ring = alarm[i].alarm_id;
            }
        }
    } else {
        alarm_recent_index = -1;
    }
	
	printf("alarm_set_recent_to_rtc  alarm_recent_index:%d\n",alarm_recent_index);

    /* set alarm to rtc */
    if(alarm_recent_index >= 0) {
        rtc_recent.tm_sec  = 5; // make sure tm_min must valid
        seconds_to_time(second_recent, &rtc_recent);

        printf("alarm_set_recent_to_rtc: %02d/%02d %02d:%02d\n",
               rtc_recent.tm_mon,
               rtc_recent.tm_mday,
               rtc_recent.tm_hour,
               rtc_recent.tm_min);

        Hal_Rtc_Set_Alarm(RTC_ALARM_1, &rtc_recent, alarm_ring);
        Hal_Rtc_Enable_Alarm(RTC_ALARM_1, true);
	#if USE_LV_WATCH_SCHEDULE_POWERON_DEMO !=0
		set_alarm_on_status(0);
	#else
        set_alarm_on_status(1);
	#endif
    } else {
        Hal_Rtc_Enable_Alarm(RTC_ALARM_1, false);
        set_alarm_on_status(0);
    }

    alarm_set_alarm_last_index(alarm_recent_index);
}

int get_alarm_on_status(void)
{
	return alarm_on_flag;
}

int set_alarm_on_status(uint8_t onoff)
{
	alarm_on_flag = onoff;
}

bool is_power_off_alarm(void)
{
	return is_poweroff_alarm;
}

bool is_power_off_alarm_wating(void)

{
	return is_poweroff_alarm_waiting;
}

void set_power_off_alarm_wating(bool onoff)

{
	is_poweroff_alarm_waiting = onoff;
}
void set_power_off_alarm(bool onoff)

{
	is_poweroff_alarm = onoff;
}

void stop_alarm_rering_timer(void)
{
	if(mmi_rering_alarm_Timer!=NULL)
		uos_timer_stop(mmi_rering_alarm_Timer);
}

#if 1 //USE_WATCH_FACE_UNLOCK != 0
bool get_alarm_is_ringing(void)
{
	return alarm_is_ringing;
}
#endif

#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0|| USE_LV_WATCH_SETTING_POWERON_OFF !=0
static bool gIs_silent_poweronoff = false;
void set_alarm_make_schedule_poweronoff_silent(bool state)
{
	gIs_silent_poweronoff=state;
}
bool get_alarm_schedule_poweronoff_is_silent(void)
{
	#if USE_LV_WATCH_SCHEDULE_POWERON_OFF_SILENT!=0
	return gIs_silent_poweronoff;
	#else
	return false;
	#endif
}
#endif

#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
bool alarm_is_in_holiday(void)
{
	bool ret=false;
    hal_rtc_t rtc_curr;
    Hal_Rtc_Gettime(&rtc_curr);
	for(int i=0;i<ALARM_MAX_HOLIDAYS_NUM;i++)
	{
		if((rtc_curr.tm_year==gAlarmHoliday[i].year)&&(rtc_curr.tm_mon==gAlarmHoliday[i].month)&&(rtc_curr.tm_mday==gAlarmHoliday[i].day))
		{
			ret=true;
			printf("alarm_is_in_holiday.find is holiday\n");
			break;
		}

	}
	return ret;
}
#endif

#if USE_LV_WATCH_FIND_ACT_TIP!=0
 static void jh_card_find_alarm_ring_exit(lv_task_t * task)
 {
 	lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_ALARM);
	 if(activity_obj) {
		 lv_watch_go_back();
	 }
 }

 void jh_card_find_alarm_ring_ui_refresh(void)
{
	
	WS_PRINTF("jh_card_find_alarm_ring_ui_refresh-- \n");
		/* activity obj */
		lv_obj_t * activity_obj=NULL;
		
		lv_watch_activity_ext_t activity_ext;
		memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
		activity_ext.actId = ACT_ID_ALARM;
		activity_ext.create = jh_card_find_alarm_ring_ui_refresh;
		activity_ext.prepare_destory = alarm_prepare_destory;
		activity_obj = lv_watch_creat_activity_obj(&activity_ext);
	
		/* watch obj */
		lv_obj_t * obj = lv_watch_obj_create(activity_obj);
		lv_watch_obj_set_anim_mode(obj, LV_WATCH_ANIM_HOR_RIGHT_HIDE);
		lv_alarm_obj_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_alarm_obj_ext_t));
		
#if USE_LV_WATCH_ANMO !=0
		if(get_anmo_status()){
			Hal_Vibrator_Play_End();
		}
#endif
	
		/* gif open */
		ext->gif_info = gif_open(obj, ANIM_ALARM, 0);
		if(!ext->gif_info) {
			printf("open anim_alarm gif failed.\n");
			return(NULL);
		}
		
#if 1 //USE_WATCH_FACE_UNLOCK != 0
		alarm_is_ringing = true;
#endif
		
		lv_obj_t * label = lv_label_create(obj, NULL);
		hal_rtc_t rtc_curr; 		   // current rtc time
		char time_info[30];
		{
			sprintf(time_info, "%s", "找到你啦！ ");
		}
		lv_label_set_text(label, time_info);
		lv_obj_set_width(label, LV_HOR_RES-20);
		lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
		lv_obj_align(label, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -20);
		lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font30);
		lv_obj_set_click(label, false);
	
		Wakeup_GuiTask(true);
		watch_set_suspend_enable(false, ACT_ID_ALARM, 0);
	
		Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
	
		lv_task_once(lv_task_create(jh_card_find_alarm_ring_exit, 30*1000, LV_TASK_PRIO_MID, (void *)0));

}


#endif


#endif /*USE_LV_WATCH_ALARM*/
