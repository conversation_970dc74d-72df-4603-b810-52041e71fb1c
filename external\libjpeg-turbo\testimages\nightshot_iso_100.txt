libjpeg-turbo note:  This image was extracted from the 8-bit nightshot_iso_100
image.  The original can be downloaded at the link below.

The New Image Compression Test Set - Jan 2008
http://www.imagecompression.info/test_images

The images historically used for compression research (lena, barbra, pepper etc...) have outlived their useful life and its about time they become a part of history only. They are too small, come from data sources too old and are available in only 8-bit precision.

These images have been carefully selected to aid in image compression algorithm research and evaluation. These are photographic images chosen to come from a wide variety of sources and each one picked to stress different aspects of algorithms. Images are available in 8-bit, 16-bit and 16-bit linear variations, RGB and gray.

Images are available without any prohibitive copyright restrictions.

These images are (c) there respective owners. You are granted full redistribution and publication rights on these images provided:

1. The origin of the pictures must not be misrepresented; you must not claim that you took the original pictures. If you use, publish or redistribute them, an acknowledgment would be appreciated but is not required.
2. Altered versions must be plainly marked as such, and must not be misinterpreted as being the originals.
3. No payment is required for distribution this material, it must be available freely under the conditions stated here. That is, it is prohibited to sell the material.
4. This notice may not be removed or altered from any distribution.

Acknowledgments: A lot of people contributed a lot of time and effort in making this test set possible. Thanks to everyone who voiced their opinion in any of the discussions online. Thanks to Axel <PERSON>, <PERSON> and Niels Fröhling for their extensive help in picking images, running all the various tests etc... Thanks to Pete Fraser, Tony Story, Wayne J. Cosshall, David Coffin, Bruce Lindbloom and raw.fotosite.pl for the images which make up this set.

Sachin Garg [India]
<EMAIL>

www.sachingarg.com | www.c10n.info | www.imagecompression.info
