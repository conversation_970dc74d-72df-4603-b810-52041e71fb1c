cmake_minimum_required(VERSION 3.10)

if(DEFINED ENV{CLANG_LLVM_TOOLCHAIN})
  set(CLANG_LLVM_TOOLCHAIN $ENV{CLANG_LLVM_TOOLCHAIN})
else()
  message(FATAL_ERROR "Please set environment variable \"CLANG_LLVM_TOOLCHAIN\" first.")
endif()

LIST(APPEND CMAKE_PROGRAM_PATH ${CLANG_LLVM_TOOLCHAIN}/bin)

find_program(CMAKE_C_COMPILER NAMES clang)
set(CMAKE_C_FLAGS                "-Wall -std=c99" CACHE STRING "c flags" )
set(CMAKE_C_FLAGS_DEBUG          "-g")
set(CMAKE_C_FLAGS_MINSIZEREL     "-Os -DNDEBUG")
set(CMAKE_C_FLAGS_RELEASE        "-O3 -DNDEBUG")
set(CMAKE_C_FLAGS_RELWITHDEBINFO "-O2 -g")

find_program(CMAKE_CXX_COMPILER NAMES clang++)
set(CMAKE_CXX_FLAGS                "-Wall -std=c++11" CACHE STRING "c++ flags" )
set(CMAKE_CXX_FLAGS_DEBUG          "-g")
set(CMAKE_CXX_FLAGS_MINSIZEREL     "-Os -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE        "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O2 -g")

set(CMAKE_EXE_LINKER_FLAGS "-fuse-ld=lld" CACHE STRING "")

find_program(CMAKE_AR NAMES llvm-ar)
find_program(CMAKE_NM NAMES llvm-nm)
find_program(CMAKE_OBJDUMP NAMES llvm-objdump)
find_program(CMAKE_RANLIB NAMES llvm-ranlib)

set(SDL2_DIR $ENV{SDL2_PATH}/lib/cmake/SDL2)

# Add misc tools path
if(DEFINED ENV{MISC_TOOLS_PATH})
  set(MISC_TOOLS_PATH $ENV{MISC_TOOLS_PATH})
else()
  message(FATAL_ERROR "Please set environment variable \"MISC_TOOLS_PATH\" first.")
endif()
list(INSERT CMAKE_PROGRAM_PATH 0 ${MISC_TOOLS_PATH})
