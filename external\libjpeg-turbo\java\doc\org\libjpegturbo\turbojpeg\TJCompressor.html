<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>TJCompressor</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TJCompressor";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/libjpegturbo/turbojpeg/TJCompressor.html" target="_top">Frames</a></li>
<li><a href="TJCompressor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.libjpegturbo.turbojpeg</div>
<h2 title="Class TJCompressor" class="title">Class TJCompressor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.libjpegturbo.turbojpeg.TJCompressor</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Closeable, java.lang.AutoCloseable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">TJCompressor</span>
extends java.lang.Object
implements java.io.Closeable</pre>
<div class="block">TurboJPEG compressor</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor()">TJCompressor</a></strong>()</code>
<div class="block">Create a TurboJPEG compressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)">TJCompressor</a></strong>(java.awt.image.BufferedImage&nbsp;srcImage,
            int&nbsp;x,
            int&nbsp;y,
            int&nbsp;width,
            int&nbsp;height)</code>
<div class="block">Create a TurboJPEG compressor instance and associate the uncompressed
 source image stored in <code>srcImage</code> with the newly created
 instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int)">TJCompressor</a></strong>(byte[]&nbsp;srcImage,
            int&nbsp;width,
            int&nbsp;pitch,
            int&nbsp;height,
            int&nbsp;pixelFormat)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJCompressor(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int,%20int,%20int)">TJCompressor</a></strong>(byte[]&nbsp;srcImage,
            int&nbsp;x,
            int&nbsp;y,
            int&nbsp;width,
            int&nbsp;pitch,
            int&nbsp;height,
            int&nbsp;pixelFormat)</code>
<div class="block">Create a TurboJPEG compressor instance and associate the uncompressed
 source image stored in <code>srcImage</code> with the newly created
 instance.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#close()">close</a></strong>()</code>
<div class="block">Free the native structures associated with this compressor instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(java.awt.image.BufferedImage,%20byte[],%20int)">compress</a></strong>(java.awt.image.BufferedImage&nbsp;srcImage,
        byte[]&nbsp;dstBuf,
        int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(byte[],%20int)"><code>compress(byte[], int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(java.awt.image.BufferedImage,%20int)">compress</a></strong>(java.awt.image.BufferedImage&nbsp;srcImage,
        int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(int)"><code>compress(int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(byte[],%20int)">compress</a></strong>(byte[]&nbsp;dstBuf,
        int&nbsp;flags)</code>
<div class="block">Compress the uncompressed source image associated with this compressor
 instance and output a JPEG image to the given destination buffer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(int)">compress</a></strong>(int&nbsp;flags)</code>
<div class="block">Compress the uncompressed source image associated with this compressor
 instance and return a buffer containing a JPEG image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(java.awt.image.BufferedImage,%20byte[],%20int)">encodeYUV</a></strong>(java.awt.image.BufferedImage&nbsp;srcImage,
         byte[]&nbsp;dstBuf,
         int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)"><code>encodeYUV(byte[], int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(java.awt.image.BufferedImage,%20int)">encodeYUV</a></strong>(java.awt.image.BufferedImage&nbsp;srcImage,
         int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>encodeYUV(int, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)">encodeYUV</a></strong>(byte[]&nbsp;dstBuf,
         int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>encodeYUV(YUVImage, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int)">encodeYUV</a></strong>(int&nbsp;flags)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>encodeYUV(int, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int[],%20int)">encodeYUV</a></strong>(int[]&nbsp;strides,
         int&nbsp;flags)</code>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into separate Y, U (Cb), and V (Cr) image planes and return a
 <code>YUVImage</code> instance containing the encoded image planes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)">encodeYUV</a></strong>(int&nbsp;pad,
         int&nbsp;flags)</code>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into a unified YUV planar image buffer and return a
 <code>YUVImage</code> instance containing the encoded image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)">encodeYUV</a></strong>(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;dstImage,
         int&nbsp;flags)</code>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into a YUV planar image and store it in the given
 <code>YUVImage</code> instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#finalize()">finalize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#getCompressedSize()">getCompressedSize</a></strong>()</code>
<div class="block">Returns the size of the image (in bytes) generated by the most recent
 compress operation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setJPEGQuality(int)">setJPEGQuality</a></strong>(int&nbsp;quality)</code>
<div class="block">Set the JPEG image quality level for subsequent compress operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)">setSourceImage</a></strong>(java.awt.image.BufferedImage&nbsp;srcImage,
              int&nbsp;x,
              int&nbsp;y,
              int&nbsp;width,
              int&nbsp;height)</code>
<div class="block">Associate an uncompressed RGB or grayscale source image with this
 compressor instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int)">setSourceImage</a></strong>(byte[]&nbsp;srcImage,
              int&nbsp;width,
              int&nbsp;pitch,
              int&nbsp;height,
              int&nbsp;pixelFormat)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)">setSourceImage</a></strong>(byte[]&nbsp;srcImage,
              int&nbsp;x,
              int&nbsp;y,
              int&nbsp;width,
              int&nbsp;pitch,
              int&nbsp;height,
              int&nbsp;pixelFormat)</code>
<div class="block">Associate an uncompressed RGB, grayscale, or CMYK source image with this
 compressor instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(org.libjpegturbo.turbojpeg.YUVImage)">setSourceImage</a></strong>(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;srcImage)</code>
<div class="block">Associate an uncompressed YUV planar source image with this compressor
 instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSubsamp(int)">setSubsamp</a></strong>(int&nbsp;newSubsamp)</code>
<div class="block">Set the level of chrominance subsampling for subsequent compress/encode
 operations.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TJCompressor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJCompressor</h4>
<pre>public&nbsp;TJCompressor()
             throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG compressor instance.</div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="TJCompressor(byte[], int, int, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJCompressor</h4>
<pre>public&nbsp;TJCompressor(byte[]&nbsp;srcImage,
            int&nbsp;x,
            int&nbsp;y,
            int&nbsp;width,
            int&nbsp;pitch,
            int&nbsp;height,
            int&nbsp;pixelFormat)
             throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG compressor instance and associate the uncompressed
 source image stored in <code>srcImage</code> with the newly created
 instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>srcImage</code> - see <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> for description</dd><dd><code>x</code> - see <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> for description</dd><dd><code>y</code> - see <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> for description</dd><dd><code>width</code> - see <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> for description</dd><dd><code>pitch</code> - see <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> for description</dd><dd><code>height</code> - see <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> for description</dd><dd><code>pixelFormat</code> - pixel format of the source image (one of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#PF_RGB"><code>TJ.PF_*</code></a>)</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="TJCompressor(byte[], int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TJCompressor</h4>
<pre>@Deprecated
public&nbsp;TJCompressor(byte[]&nbsp;srcImage,
                       int&nbsp;width,
                       int&nbsp;pitch,
                       int&nbsp;height,
                       int&nbsp;pixelFormat)
             throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJCompressor(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="TJCompressor(java.awt.image.BufferedImage, int, int, int, int)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TJCompressor</h4>
<pre>public&nbsp;TJCompressor(java.awt.image.BufferedImage&nbsp;srcImage,
            int&nbsp;x,
            int&nbsp;y,
            int&nbsp;width,
            int&nbsp;height)
             throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Create a TurboJPEG compressor instance and associate the uncompressed
 source image stored in <code>srcImage</code> with the newly created
 instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>srcImage</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> for description</dd><dd><code>x</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> for description</dd><dd><code>y</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> for description</dd><dd><code>width</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> for description</dd><dd><code>height</code> - see
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> for description</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setSourceImage(byte[], int, int, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceImage</h4>
<pre>public&nbsp;void&nbsp;setSourceImage(byte[]&nbsp;srcImage,
                  int&nbsp;x,
                  int&nbsp;y,
                  int&nbsp;width,
                  int&nbsp;pitch,
                  int&nbsp;height,
                  int&nbsp;pixelFormat)
                    throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Associate an uncompressed RGB, grayscale, or CMYK source image with this
 compressor instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>srcImage</code> - image buffer containing RGB, grayscale, or CMYK pixels to
 be compressed or encoded.  This buffer is not modified.</dd><dd><code>x</code> - x offset (in pixels) of the region in the source image from which
 the JPEG or YUV image should be compressed/encoded</dd><dd><code>y</code> - y offset (in pixels) of the region in the source image from which
 the JPEG or YUV image should be compressed/encoded</dd><dd><code>width</code> - width (in pixels) of the region in the source image from
 which the JPEG or YUV image should be compressed/encoded</dd><dd><code>pitch</code> - bytes per line of the source image.  Normally, this should be
 <code>width * TJ.pixelSize(pixelFormat)</code> if the source image is
 unpadded, but you can use this parameter to, for instance, specify that
 the scanlines in the source image are padded to a 4-byte boundary or to
 compress/encode a JPEG or YUV image from a region of a larger source
 image.  You can also be clever and use this parameter to skip lines, etc.
 Setting this parameter to 0 is the equivalent of setting it to
 <code>width * TJ.pixelSize(pixelFormat)</code>.</dd><dd><code>height</code> - height (in pixels) of the region in the source image from
 which the JPEG or YUV image should be compressed/encoded</dd><dd><code>pixelFormat</code> - pixel format of the source image (one of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#PF_RGB"><code>TJ.PF_*</code></a>)</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="setSourceImage(byte[], int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceImage</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;setSourceImage(byte[]&nbsp;srcImage,
                             int&nbsp;width,
                             int&nbsp;pitch,
                             int&nbsp;height,
                             int&nbsp;pixelFormat)
                    throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>setSourceImage(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="setSourceImage(java.awt.image.BufferedImage, int, int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceImage</h4>
<pre>public&nbsp;void&nbsp;setSourceImage(java.awt.image.BufferedImage&nbsp;srcImage,
                  int&nbsp;x,
                  int&nbsp;y,
                  int&nbsp;width,
                  int&nbsp;height)
                    throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Associate an uncompressed RGB or grayscale source image with this
 compressor instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>srcImage</code> - a <code>BufferedImage</code> instance containing RGB or
 grayscale pixels to be compressed or encoded.  This image is not modified.</dd><dd><code>x</code> - x offset (in pixels) of the region in the source image from which
 the JPEG or YUV image should be compressed/encoded</dd><dd><code>y</code> - y offset (in pixels) of the region in the source image from which
 the JPEG or YUV image should be compressed/encoded</dd><dd><code>width</code> - width (in pixels) of the region in the source image from
 which the JPEG or YUV image should be compressed/encoded (0 = use the
 width of the source image)</dd><dd><code>height</code> - height (in pixels) of the region in the source image from
 which the JPEG or YUV image should be compressed/encoded (0 = use the
 height of the source image)</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="setSourceImage(org.libjpegturbo.turbojpeg.YUVImage)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceImage</h4>
<pre>public&nbsp;void&nbsp;setSourceImage(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;srcImage)
                    throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Associate an uncompressed YUV planar source image with this compressor
 instance.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>srcImage</code> - YUV planar image to be compressed.  This image is not
 modified.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="setSubsamp(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubsamp</h4>
<pre>public&nbsp;void&nbsp;setSubsamp(int&nbsp;newSubsamp)</pre>
<div class="block">Set the level of chrominance subsampling for subsequent compress/encode
 operations.  When pixels are converted from RGB to YCbCr (see
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#CS_YCbCr"><code>TJ.CS_YCbCr</code></a>) or from CMYK to YCCK (see <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#CS_YCCK"><code>TJ.CS_YCCK</code></a>) as part
 of the JPEG compression process, some of the Cb and Cr (chrominance)
 components can be discarded or averaged together to produce a smaller
 image with little perceptible loss of image clarity (the human eye is more
 sensitive to small changes in brightness than to small changes in color.)
 This is called "chrominance subsampling".
 <p>
 NOTE: This method has no effect when compressing a JPEG image from a YUV
 planar source.  In that case, the level of chrominance subsampling in
 the JPEG image is determined by the source.  Furthermore, this method has
 no effect when encoding to a pre-allocated <a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg"><code>YUVImage</code></a> instance.  In
 that case, the level of chrominance subsampling is determined by the
 destination.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>newSubsamp</code> - the level of chrominance subsampling to use in
 subsequent compress/encode oeprations (one of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#SAMP_444"><code>TJ.SAMP_*</code></a>)</dd></dl>
</li>
</ul>
<a name="setJPEGQuality(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJPEGQuality</h4>
<pre>public&nbsp;void&nbsp;setJPEGQuality(int&nbsp;quality)</pre>
<div class="block">Set the JPEG image quality level for subsequent compress operations.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>quality</code> - the new JPEG image quality level (1 to 100, 1 = worst,
 100 = best)</dd></dl>
</li>
</ul>
<a name="compress(byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compress</h4>
<pre>public&nbsp;void&nbsp;compress(byte[]&nbsp;dstBuf,
            int&nbsp;flags)
              throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Compress the uncompressed source image associated with this compressor
 instance and output a JPEG image to the given destination buffer.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>dstBuf</code> - buffer that will receive the JPEG image.  Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#bufSize(int,%20int,%20int)"><code>TJ.bufSize(int, int, int)</code></a> to determine the maximum size for this buffer based on
 the source image's width and height and the desired level of chrominance
 subsampling.</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="compress(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compress</h4>
<pre>public&nbsp;byte[]&nbsp;compress(int&nbsp;flags)
                throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Compress the uncompressed source image associated with this compressor
 instance and return a buffer containing a JPEG image.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a buffer containing a JPEG image.  The length of this buffer will
 not be equal to the size of the JPEG image.  Use <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#getCompressedSize()"><code>getCompressedSize()</code></a> to obtain the size of the JPEG image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="compress(java.awt.image.BufferedImage, byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compress</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;compress(java.awt.image.BufferedImage&nbsp;srcImage,
                       byte[]&nbsp;dstBuf,
                       int&nbsp;flags)
              throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(byte[],%20int)"><code>compress(byte[], int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="compress(java.awt.image.BufferedImage, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compress</h4>
<pre>@Deprecated
public&nbsp;byte[]&nbsp;compress(java.awt.image.BufferedImage&nbsp;srcImage,
                         int&nbsp;flags)
                throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#compress(int)"><code>compress(int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(org.libjpegturbo.turbojpeg.YUVImage, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>public&nbsp;void&nbsp;encodeYUV(<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;dstImage,
             int&nbsp;flags)
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into a YUV planar image and store it in the given
 <code>YUVImage</code> instance.   This method uses the accelerated color
 conversion routines in TurboJPEG's underlying codec but does not execute
 any of the other steps in the JPEG compression process.  Encoding
 CMYK source images to YUV is not supported.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>dstImage</code> - <a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg"><code>YUVImage</code></a> instance that will receive the YUV planar
 image</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;encodeYUV(byte[]&nbsp;dstBuf,
                        int&nbsp;flags)
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>encodeYUV(YUVImage, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>public&nbsp;<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;encodeYUV(int&nbsp;pad,
                 int&nbsp;flags)
                   throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into a unified YUV planar image buffer and return a
 <code>YUVImage</code> instance containing the encoded image.  This method
 uses the accelerated color conversion routines in TurboJPEG's underlying
 codec but does not execute any of the other steps in the JPEG compression
 process.  Encoding CMYK source images to YUV is not supported.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>pad</code> - the width of each line in each plane of the YUV image will be
 padded to the nearest multiple of this number of bytes (must be a power of
 2.)</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a YUV planar image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(int[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>public&nbsp;<a href="../../../org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a>&nbsp;encodeYUV(int[]&nbsp;strides,
                 int&nbsp;flags)
                   throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into separate Y, U (Cb), and V (Cr) image planes and return a
 <code>YUVImage</code> instance containing the encoded image planes.  This
 method uses the accelerated color conversion routines in TurboJPEG's
 underlying codec but does not execute any of the other steps in the JPEG
 compression process.  Encoding CMYK source images to YUV is not supported.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>strides</code> - an array of integers, each specifying the number of bytes
 per line in the corresponding plane of the output image.  Setting the
 stride for any plane to 0 is the same as setting it to the component width
 of the plane.  If <code>strides</code> is null, then the strides for all
 planes will be set to their respective component widths.  You can adjust
 the strides in order to add an arbitrary amount of line padding to each
 plane.</dd><dd><code>flags</code> - the bitwise OR of one or more of
 <a href="../../../org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP"><code>TJ.FLAG_*</code></a></dd>
<dt><span class="strong">Returns:</span></dt><dd>a YUV planar image.</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>@Deprecated
public&nbsp;byte[]&nbsp;encodeYUV(int&nbsp;flags)
                 throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>encodeYUV(int, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(java.awt.image.BufferedImage, byte[], int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;encodeYUV(java.awt.image.BufferedImage&nbsp;srcImage,
                        byte[]&nbsp;dstBuf,
                        int&nbsp;flags)
               throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)"><code>encodeYUV(byte[], int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="encodeYUV(java.awt.image.BufferedImage, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeYUV</h4>
<pre>@Deprecated
public&nbsp;byte[]&nbsp;encodeYUV(java.awt.image.BufferedImage&nbsp;srcImage,
                          int&nbsp;flags)
                 throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block"><span class="strong">Deprecated.</span>&nbsp;<i>Use
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="../../../org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>encodeYUV(int, int)</code></a> instead.</i></div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="getCompressedSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompressedSize</h4>
<pre>public&nbsp;int&nbsp;getCompressedSize()</pre>
<div class="block">Returns the size of the image (in bytes) generated by the most recent
 compress operation.</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>the size of the image (in bytes) generated by the most recent
 compress operation.</dd></dl>
</li>
</ul>
<a name="close()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()
           throws <a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></pre>
<div class="block">Free the native structures associated with this compressor instance.</div>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>close</code>&nbsp;in interface&nbsp;<code>java.io.Closeable</code></dd>
<dt><strong>Specified by:</strong></dt>
<dd><code>close</code>&nbsp;in interface&nbsp;<code>java.lang.AutoCloseable</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></code></dd></dl>
</li>
</ul>
<a name="finalize()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>finalize</h4>
<pre>protected&nbsp;void&nbsp;finalize()
                 throws java.lang.Throwable</pre>
<dl>
<dt><strong>Overrides:</strong></dt>
<dd><code>finalize</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>java.lang.Throwable</code></dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/libjpegturbo/turbojpeg/TJCompressor.html" target="_top">Frames</a></li>
<li><a href="TJCompressor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
