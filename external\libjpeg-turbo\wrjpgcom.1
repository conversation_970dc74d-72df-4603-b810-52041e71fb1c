.TH WRJPGCOM 1 "15 June 1995"
.SH NAME
wrjpgcom \- insert text comments into a JPEG file
.SH SYNOPSIS
.B wrjpgcom
[
.B \-replace
]
[
.BI \-comment " text"
]
[
.BI \-cfile " name"
]
[
.I filename
]
.LP
.SH DESCRIPTION
.LP
.B wrjpg<PERSON>
reads the named JPEG/JFIF file, or the standard input if no file is named,
and generates a new JPEG/JFIF file on standard output.  A comment block is
added to the file.
.PP
The JPEG standard allows "comment" (COM) blocks to occur within a JPEG file.
Although the standard doesn't actually define what COM blocks are for, they
are widely used to hold user-supplied text strings.  This lets you add
annotations, titles, index terms, etc to your JPEG files, and later retrieve
them as text.  COM blocks do not interfere with the image stored in the JPEG
file.  The maximum size of a COM block is 64K, but you can have as many of
them as you like in one JPEG file.
.PP
.B wrjpg<PERSON>
adds a COM block, containing text you provide, to a JPEG file.
Ordinarily, the COM block is added after any existing COM blocks; but you
can delete the old COM blocks if you wish.
.SH OPTIONS
Switch names may be abbreviated, and are not case sensitive.
.TP
.B \-replace
Delete any existing COM blocks from the file.
.TP
.BI \-comment " text"
Supply text for new COM block on command line.
.TP
.BI \-cfile " name"
Read text for new COM block from named file.
.PP
If you have only one line of comment text to add, you can provide it on the
command line with
.BR \-comment .
The comment text must be surrounded with quotes so that it is treated as a
single argument.  Longer comments can be read from a text file.
.PP
If you give neither
.B \-comment
nor
.BR \-cfile,
then
.B wrjpgcom
will read the comment text from standard input.  (In this case an input image
file name MUST be supplied, so that the source JPEG file comes from somewhere
else.)  You can enter multiple lines, up to 64KB worth.  Type an end-of-file
indicator (usually control-D) to terminate the comment text entry.
.PP
.B wrjpgcom
will not add a COM block if the provided comment string is empty.  Therefore
\fB\-replace \-comment ""\fR can be used to delete all COM blocks from a file.
.SH EXAMPLES
.LP
Add a short comment to in.jpg, producing out.jpg:
.IP
.B wrjpgcom \-c
\fI"View of my back yard" in.jpg
.B >
.I out.jpg
.PP
Attach a long comment previously stored in comment.txt:
.IP
.B wrjpgcom
.I in.jpg
.B <
.I comment.txt
.B >
.I out.jpg
.PP
or equivalently
.IP
.B wrjpgcom
.B -cfile
.I comment.txt
.B <
.I in.jpg
.B >
.I out.jpg
.SH SEE ALSO
.BR cjpeg (1),
.BR djpeg (1),
.BR jpegtran (1),
.BR rdjpgcom (1)
.SH AUTHOR
Independent JPEG Group
