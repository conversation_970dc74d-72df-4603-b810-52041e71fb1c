.TH CJPEG 1 "18 March 2017"
.SH NAME
cjpeg \- compress an image file to a JPEG file
.SH SYNOPSIS
.B cjpeg
[
.I options
]
[
.I filename
]
.LP
.SH DESCRIPTION
.LP
.B cjpeg
compresses the named image file, or the standard input if no file is
named, and produces a JPEG/JFIF file on the standard output.
The currently supported input file formats are: PPM (PBMPLUS color
format), PGM (PBMPLUS grayscale format), BMP, Targa, and RLE (Utah Raster
Toolkit format).  (RLE is supported only if the URT library is available.)
.SH OPTIONS
All switch names may be abbreviated; for example,
.B \-grayscale
may be written
.B \-gray
or
.BR \-gr .
Most of the "basic" switches can be abbreviated to as little as one letter.
Upper and lower case are equivalent (thus
.B \-BMP
is the same as
.BR \-bmp ).
British spellings are also accepted (e.g.,
.BR \-greyscale ),
though for brevity these are not mentioned below.
.PP
The basic switches are:
.TP
.BI \-quality " N[,...]"
Scale quantization tables to adjust image quality.  Quality is 0 (worst) to
100 (best); default is 75.  (See below for more info.)
.TP
.B \-grayscale
Create monochrome JPEG file from color input.  Be sure to use this switch when
compressing a grayscale BMP file, because
.B cjpeg
isn't bright enough to notice whether a BMP file uses only shades of gray.
By saying
.BR \-grayscale,
you'll get a smaller JPEG file that takes less time to process.
.TP
.B \-rgb
Create RGB JPEG file.
Using this switch suppresses the conversion from RGB
colorspace input to the default YCbCr JPEG colorspace.
.TP
.B \-optimize
Perform optimization of entropy encoding parameters.  Without this, default
encoding parameters are used.
.B \-optimize
usually makes the JPEG file a little smaller, but
.B cjpeg
runs somewhat slower and needs much more memory.  Image quality and speed of
decompression are unaffected by
.BR \-optimize .
.TP
.B \-progressive
Create progressive JPEG file (see below).
.TP
.B \-targa
Input file is Targa format.  Targa files that contain an "identification"
field will not be automatically recognized by
.BR cjpeg ;
for such files you must specify
.B \-targa
to make
.B cjpeg
treat the input as Targa format.
For most Targa files, you won't need this switch.
.PP
The
.B \-quality
switch lets you trade off compressed file size against quality of the
reconstructed image: the higher the quality setting, the larger the JPEG file,
and the closer the output image will be to the original input.  Normally you
want to use the lowest quality setting (smallest file) that decompresses into
something visually indistinguishable from the original image.  For this
purpose the quality setting should generally be between 50 and 95 (the default
is 75) for photographic images.  If you see defects at
.B \-quality
75, then go up 5 or 10 counts at a time until you are happy with the output
image.  (The optimal setting will vary from one image to another.)
.PP
.B \-quality
100 will generate a quantization table of all 1's, minimizing loss in the
quantization step (but there is still information loss in subsampling, as well
as roundoff error.)  For most images, specifying a quality value above
about 95 will increase the size of the compressed file dramatically, and while
the quality gain from these higher quality values is measurable (using metrics
such as PSNR or SSIM), it is rarely perceivable by human vision.
.PP
In the other direction, quality values below 50 will produce very small files
of low image quality.  Settings around 5 to 10 might be useful in preparing an
index of a large image library, for example.  Try
.B \-quality
2 (or so) for some amusing Cubist effects.  (Note: quality
values below about 25 generate 2-byte quantization tables, which are
considered optional in the JPEG standard.
.B cjpeg
emits a warning message when you give such a quality value, because some
other JPEG programs may be unable to decode the resulting file.  Use
.B \-baseline
if you need to ensure compatibility at low quality values.)
.PP
The \fB-quality\fR option has been extended in this version of \fBcjpeg\fR to
support separate quality settings for luminance and chrominance (or, in
general, separate settings for every quantization table slot.)  The principle
is the same as chrominance subsampling:  since the human eye is more sensitive
to spatial changes in brightness than spatial changes in color, the chrominance
components can be quantized more than the luminance components without
incurring any visible image quality loss.  However, unlike subsampling, this
feature reduces data in the frequency domain instead of the spatial domain,
which allows for more fine-grained control.  This option is useful in
quality-sensitive applications, for which the artifacts generated by
subsampling may be unacceptable.
.PP
The \fB-quality\fR option accepts a comma-separated list of parameters, which
respectively refer to the quality levels that should be assigned to the
quantization table slots.  If there are more q-table slots than parameters,
then the last parameter is replicated.  Thus, if only one quality parameter is
given, this is used for both luminance and chrominance (slots 0 and 1,
respectively), preserving the legacy behavior of cjpeg v6b and prior.
More (or customized) quantization tables can be set with the \fB-qtables\fR
option and assigned to components with the \fB-qslots\fR option (see the
"wizard" switches below.)
.PP
JPEG files generated with separate luminance and chrominance quality are fully
compliant with standard JPEG decoders.
.PP
.BR CAUTION:
For this setting to be useful, be sure to pass an argument of \fB-sample 1x1\fR
to \fBcjpeg\fR to disable chrominance subsampling.  Otherwise, the default
subsampling level (2x2, AKA "4:2:0") will be used.
.PP
The
.B \-progressive
switch creates a "progressive JPEG" file.  In this type of JPEG file, the data
is stored in multiple scans of increasing quality.  If the file is being
transmitted over a slow communications link, the decoder can use the first
scan to display a low-quality image very quickly, and can then improve the
display with each subsequent scan.  The final image is exactly equivalent to a
standard JPEG file of the same quality setting, and the total file size is
about the same --- often a little smaller.
.PP
Switches for advanced users:
.TP
.B \-arithmetic
Use arithmetic coding.
.B Caution:
arithmetic coded JPEG is not yet widely implemented, so many decoders will be
unable to view an arithmetic coded JPEG file at all.
.TP
.B \-dct int
Use integer DCT method (default).
.TP
.B \-dct fast
Use fast integer DCT (less accurate).
In libjpeg-turbo, the fast method is generally about 5-15% faster than the int
method when using the x86/x86-64 SIMD extensions (results may vary with other
SIMD implementations, or when using libjpeg-turbo without SIMD extensions.)
For quality levels of 90 and below, there should be little or no perceptible
difference between the two algorithms.  For quality levels above 90, however,
the difference between the fast and the int methods becomes more pronounced.
With quality=97, for instance, the fast method incurs generally about a 1-3 dB
loss (in PSNR) relative to the int method, but this can be larger for some
images.  Do not use the fast method with quality levels above 97.  The
algorithm often degenerates at quality=98 and above and can actually produce a
more lossy image than if lower quality levels had been used.  Also, in
libjpeg-turbo, the fast method is not fully accelerated for quality levels
above 97, so it will be slower than the int method.
.TP
.B \-dct float
Use floating-point DCT method.
The float method is mainly a legacy feature.  It does not produce significantly
more accurate results than the int method, and it is much slower.  The float
method may also give different results on different machines due to varying
roundoff behavior, whereas the integer methods should give the same results on
all machines.
.TP
.BI \-icc " file"
Embed ICC color management profile contained in the specified file.
.TP
.BI \-restart " N"
Emit a JPEG restart marker every N MCU rows, or every N MCU blocks if "B" is
attached to the number.
.B \-restart 0
(the default) means no restart markers.
.TP
.BI \-smooth " N"
Smooth the input image to eliminate dithering noise.  N, ranging from 1 to
100, indicates the strength of smoothing.  0 (the default) means no smoothing.
.TP
.BI \-maxmemory " N"
Set limit for amount of memory to use in processing large images.  Value is
in thousands of bytes, or millions of bytes if "M" is attached to the
number.  For example,
.B \-max 4m
selects 4000000 bytes.  If more space is needed, an error will occur.
.TP
.BI \-outfile " name"
Send output image to the named file, not to standard output.
.TP
.BI \-memdst
Compress to memory instead of a file.  This feature was implemented mainly as a
way of testing the in-memory destination manager (jpeg_mem_dest()), but it is
also useful for benchmarking, since it reduces the I/O overhead.
.TP
.B \-verbose
Enable debug printout.  More
.BR \-v 's
give more output.  Also, version information is printed at startup.
.TP
.B \-debug
Same as
.BR \-verbose .
.TP
.B \-version
Print version information and exit.
.PP
The
.B \-restart
option inserts extra markers that allow a JPEG decoder to resynchronize after
a transmission error.  Without restart markers, any damage to a compressed
file will usually ruin the image from the point of the error to the end of the
image; with restart markers, the damage is usually confined to the portion of
the image up to the next restart marker.  Of course, the restart markers
occupy extra space.  We recommend
.B \-restart 1
for images that will be transmitted across unreliable networks such as Usenet.
.PP
The
.B \-smooth
option filters the input to eliminate fine-scale noise.  This is often useful
when converting dithered images to JPEG: a moderate smoothing factor of 10 to
50 gets rid of dithering patterns in the input file, resulting in a smaller
JPEG file and a better-looking image.  Too large a smoothing factor will
visibly blur the image, however.
.PP
Switches for wizards:
.TP
.B \-baseline
Force baseline-compatible quantization tables to be generated.  This clamps
quantization values to 8 bits even at low quality settings.  (This switch is
poorly named, since it does not ensure that the output is actually baseline
JPEG.  For example, you can use
.B \-baseline
and
.B \-progressive
together.)
.TP
.BI \-qtables " file"
Use the quantization tables given in the specified text file.
.TP
.BI \-qslots " N[,...]"
Select which quantization table to use for each color component.
.TP
.BI \-sample " HxV[,...]"
Set JPEG sampling factors for each color component.
.TP
.BI \-scans " file"
Use the scan script given in the specified text file.
.PP
The "wizard" switches are intended for experimentation with JPEG.  If you
don't know what you are doing, \fBdon't use them\fR.  These switches are
documented further in the file wizard.txt.
.SH EXAMPLES
.LP
This example compresses the PPM file foo.ppm with a quality factor of
60 and saves the output as foo.jpg:
.IP
.B cjpeg \-quality
.I 60 foo.ppm
.B >
.I foo.jpg
.SH HINTS
Color GIF files are not the ideal input for JPEG; JPEG is really intended for
compressing full-color (24-bit) images.  In particular, don't try to convert
cartoons, line drawings, and other images that have only a few distinct
colors.  GIF works great on these, JPEG does not.  If you want to convert a
GIF to JPEG, you should experiment with
.BR cjpeg 's
.B \-quality
and
.B \-smooth
options to get a satisfactory conversion.
.B \-smooth 10
or so is often helpful.
.PP
Avoid running an image through a series of JPEG compression/decompression
cycles.  Image quality loss will accumulate; after ten or so cycles the image
may be noticeably worse than it was after one cycle.  It's best to use a
lossless format while manipulating an image, then convert to JPEG format when
you are ready to file the image away.
.PP
The
.B \-optimize
option to
.B cjpeg
is worth using when you are making a "final" version for posting or archiving.
It's also a win when you are using low quality settings to make very small
JPEG files; the percentage improvement is often a lot more than it is on
larger files.  (At present,
.B \-optimize
mode is always selected when generating progressive JPEG files.)
.SH ENVIRONMENT
.TP
.B JPEGMEM
If this environment variable is set, its value is the default memory limit.
The value is specified as described for the
.B \-maxmemory
switch.
.B JPEGMEM
overrides the default value specified when the program was compiled, and
itself is overridden by an explicit
.BR \-maxmemory .
.SH SEE ALSO
.BR djpeg (1),
.BR jpegtran (1),
.BR rdjpgcom (1),
.BR wrjpgcom (1)
.br
.BR ppm (5),
.BR pgm (5)
.br
Wallace, Gregory K.  "The JPEG Still Picture Compression Standard",
Communications of the ACM, April 1991 (vol. 34, no. 4), pp. 30-44.
.SH AUTHOR
Independent JPEG Group
.PP
This file was modified by The libjpeg-turbo Project to include only information
relevant to libjpeg-turbo, to wordsmith certain sections, and to describe
features not present in libjpeg.
.SH ISSUES
Support for GIF input files was removed in cjpeg v6b due to concerns over
the Unisys LZW patent.  Although this patent expired in 2006, cjpeg still
lacks GIF support, for these historical reasons.  (Conversion of GIF files to
JPEG is usually a bad idea anyway, since GIF is a 256-color format.)
.PP
Not all variants of BMP and Targa file formats are supported.
.PP
The
.B \-targa
switch is not a bug, it's a feature.  (It would be a bug if the Targa format
designers had not been clueless.)
