EXPORTS
  jcopy_block_row @ 1 ;
  jcopy_sample_rows @ 2 ;
  jdiv_round_up @ 3 ;
  jinit_1pass_quantizer @ 4 ;
  jinit_2pass_quantizer @ 5 ;
  jinit_c_coef_controller @ 6 ;
  jinit_c_main_controller @ 7 ;
  jinit_c_master_control @ 8 ;
  jinit_c_prep_controller @ 9 ;
  jinit_color_converter @ 10 ;
  jinit_color_deconverter @ 11 ;
  jinit_compress_master @ 12 ;
  jinit_d_coef_controller @ 13 ;
  jinit_d_main_controller @ 14 ;
  jinit_d_post_controller @ 15 ;
  jinit_downsampler @ 16 ;
  jinit_forward_dct @ 17 ;
  jinit_huff_decoder @ 18 ;
  jinit_huff_encoder @ 19 ;
  jinit_input_controller @ 20 ;
  jinit_inverse_dct @ 21 ;
  jinit_marker_reader @ 22 ;
  jinit_marker_writer @ 23 ;
  jinit_master_decompress @ 24 ;
  jinit_memory_mgr @ 25 ;
  jinit_merged_upsampler @ 26 ;
  jinit_phuff_decoder @ 27 ;
  jinit_phuff_encoder @ 28 ;
  jinit_upsampler @ 29 ;
  jpeg_CreateCompress @ 30 ;
  jpeg_CreateDecompress @ 31 ;
  jpeg_abort @ 32 ;
  jpeg_abort_compress @ 33 ;
  jpeg_abort_decompress @ 34 ;
  jpeg_add_quant_table @ 35 ;
  jpeg_alloc_huff_table @ 36 ;
  jpeg_alloc_quant_table @ 37 ;
  jpeg_calc_jpeg_dimensions @ 38 ;
  jpeg_calc_output_dimensions @ 39 ;
  jpeg_consume_input @ 40 ;
  jpeg_copy_critical_parameters @ 41 ;
  jpeg_default_colorspace @ 42 ;
  jpeg_default_qtables @ 43 ;
  jpeg_destroy @ 44 ;
  jpeg_destroy_compress @ 45 ;
  jpeg_destroy_decompress @ 46 ;
  jpeg_fdct_float @ 47 ;
  jpeg_fdct_ifast @ 48 ;
  jpeg_fdct_islow @ 49 ;
  jpeg_fill_bit_buffer @ 50 ;
  jpeg_finish_compress @ 51 ;
  jpeg_finish_decompress @ 52 ;
  jpeg_finish_output @ 53 ;
  jpeg_free_large @ 54 ;
  jpeg_free_small @ 55 ;
  jpeg_gen_optimal_table @ 56 ;
  jpeg_get_large @ 57 ;
  jpeg_get_small @ 58 ;
  jpeg_has_multiple_scans @ 59 ;
  jpeg_huff_decode @ 60 ;
  jpeg_idct_1x1 @ 61 ;
  jpeg_idct_2x2 @ 62 ;
  jpeg_idct_4x4 @ 63 ;
  jpeg_idct_float @ 64 ;
  jpeg_idct_ifast @ 65 ;
  jpeg_idct_islow @ 66 ;
  jpeg_input_complete @ 67 ;
  jpeg_make_c_derived_tbl @ 68 ;
  jpeg_make_d_derived_tbl @ 69 ;
  jpeg_mem_available @ 70 ;
  jpeg_mem_init @ 71 ;
  jpeg_mem_term @ 72 ;
  jpeg_new_colormap @ 73 ;
  jpeg_open_backing_store @ 74 ;
  jpeg_quality_scaling @ 75 ;
  jpeg_read_coefficients @ 76 ;
  jpeg_read_header @ 77 ;
  jpeg_read_raw_data @ 78 ;
  jpeg_read_scanlines @ 79 ;
  jpeg_resync_to_restart @ 80 ;
  jpeg_save_markers @ 81 ;
  jpeg_set_colorspace @ 82 ;
  jpeg_set_defaults @ 83 ;
  jpeg_set_linear_quality @ 84 ;
  jpeg_set_marker_processor @ 85 ;
  jpeg_set_quality @ 86 ;
  jpeg_simple_progression @ 87 ;
  jpeg_start_compress @ 88 ;
  jpeg_start_decompress @ 89 ;
  jpeg_start_output @ 90 ;
  jpeg_std_error @ 91 ;
  jpeg_stdio_dest @ 92 ;
  jpeg_stdio_src @ 93 ;
  jpeg_suppress_tables @ 94 ;
  jpeg_write_coefficients @ 95 ;
  jpeg_write_m_byte @ 96 ;
  jpeg_write_m_header @ 97 ;
  jpeg_write_marker @ 98 ;
  jpeg_write_raw_data @ 99 ;
  jpeg_write_scanlines @ 100 ;
  jpeg_write_tables @ 101 ;
  jround_up @ 102 ;
  jzero_far @ 103 ;
  jpeg_skip_scanlines @ 104 ;
  jpeg_crop_scanline @ 105 ;
  jpeg_read_icc_profile @ 106 ;
  jpeg_write_icc_profile @ 107 ;
