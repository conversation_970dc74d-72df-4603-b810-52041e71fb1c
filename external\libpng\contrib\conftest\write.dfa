# write.dfa
#  Build time configuration of libpng
#
# Author: <PERSON>
# Copyright: (c) <PERSON>, 2013
# Usage rights:
#  To the extent possible under law, the author has waived all copyright and
#  related or neighboring rights to this work.  This work is published from:
#  United States.
#
# Build libpng with no read support and minimal write support.
#

everything = off

# Switch on the write code - this makes a minimalist encoder

option WRITE on

# Choose fixed or floating point APIs and arithmetic.  The choices are
# independent but normally they will match.  It is typically better to use the
# floating point if you have floating point hardware.  If you don't know, or
# (perhaps) to make libpng smaller used fixed point throughout.

#Fixed point:
#option FIXED_POINT on
#option FLOATING_ARITHMETIC off

#Floating point:
option FLOATING_POINT on
option FLOATING_ARITHMETIC on

# Basic error handling, IO and user memory support.  The latter allows the
# application program to provide its own implementations of 'malloc' and 'free'.
option SETJMP on
option STDIO on
option USER_MEM on

# Everything else is optional.  Unlike the read code in libpng the write code
# does not need to deal with arbitrary formats, so only add support for things
# you really do write!  For example you might only write sRGB images, sometimes
# with transparency and never write 16 bit images, so:
option WRITE_sRGB on
option WRITE_tRNS on
#option WRITE_16BIT off (this is the default with 'everything = off')
