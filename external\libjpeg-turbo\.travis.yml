language: c

branches:
  except:
    - /^[0-9]+\.[0-9]+\.[0-9]+/
    - /^jpeg-.*/

matrix:
  include:
    - os: linux
      env: BUILD_OFFICIAL=1
      sudo: required
      services:
        - docker
    - os: osx
      env: BUILD_OFFICIAL=1
      osx_image: xcode7.3
    - os: linux
      dist: trusty
      compiler: clang
      env:
        CMAKE_BUILD_TYPE=RelWithDebInfo
        CFLAGS_RELWITHDEBINFO="-O1 -g -fsanitize=address -fno-omit-frame-pointer"
        CMAKE_FLAGS="-DENABLE_SHARED=0"
        ASAN_OPTIONS="detect_leaks=1 symbolize=1"
      addons:
        apt:
          packages:
            - nasm
    - os: linux
      dist: trusty
      compiler: gcc
      env: CMAKE_FLAGS="-DWITH_12BIT=1"
    - os: linux
      dist: trusty
      compiler: gcc
      env: CMAKE_FLAGS="-DWITH_JPEG7=1"
      addons:
        apt:
          packages:
            - nasm
    - os: linux
      dist: trusty
      compiler: gcc
      env: CMAKE_FLAGS="-DWITH_JPEG8=1"
      addons:
        apt:
          packages:
            - nasm
    - os: linux
      dist: trusty
      compiler: gcc
      env: CMAKE_FLAGS="-DWITH_SIMD=0"

addons:
  homebrew:
    brewfile: true
    update: true

before_install:
  - if [ "$TRAVIS_OS_NAME" = "osx" ]; then
      ln -fs /usr/local/bin/gpg1 /usr/local/bin/gpg &&
      git clone --depth=1 https://github.com/libjpeg-turbo/gas-preprocessor.git ~/src/gas-preprocessor &&
      ln -fs /Applications/Xcode.app /Applications/Xcode72.app;
    fi
  - if [ "${BUILD_OFFICIAL:-}" != "" ]; then
      if [ "$TRAVIS_OS_NAME" = "linux" ]; then
        docker pull dcommander/buildljt;
      fi &&
      git clone --depth=1 https://github.com/libjpeg-turbo/buildscripts.git -b $TRAVIS_BRANCH ~/src/buildscripts &&
      if [ -n "$encrypted_f92e8533f6f1_iv" ]; then
        openssl aes-256-cbc -K $encrypted_f92e8533f6f1_key -iv $encrypted_f92e8533f6f1_iv -in ci/keys.enc -out ci/keys -d &&
        tar xf ci/keys &&
        rm ci/keys &&
        mv ci/gpgsign ~/src/buildscripts &&
        gpg --import ci/sign_ljt &&
        rm ci/sign_ljt;
      fi
    fi

script:
  - if [ "${BUILD_OFFICIAL:-}" != "" ]; then
      mkdir -p ~/src/ljt.nightly &&
      if [ "$TRAVIS_OS_NAME" = "linux" ]; then
        mkdir $HOME/rpmkeys &&
        wget --no-check-certificate "http://www.libjpeg-turbo.org/key/LJTPR-GPG-KEY" -O $HOME/rpmkeys/LJTPR-GPG-KEY &&
        docker run -v $HOME/src/ljt.nightly:/root/src/ljt.nightly -v $HOME/src/buildscripts:/root/src/buildscripts -v $TRAVIS_BUILD_DIR:/root/src/libjpeg-turbo -v $HOME/.gnupg:/root/.gnupg -v $HOME/rpmkeys:/rpmkeys -t dcommander/buildljt:latest bash -c "rpm --import /rpmkeys/LJTPR-GPG-KEY && ~/src/buildscripts/buildljt -d /root/src/libjpeg-turbo -v" &&
        sudo chown -R travis:travis ~/src/ljt.nightly &&
        mv ~/src/ljt.nightly/latest/log-$TRAVIS_OS_NAME.txt ~/src/ljt.nightly/latest/files/;
      else
        PATH=$PATH:~/src/gas-preprocessor ~/src/buildscripts/buildljt -d $TRAVIS_BUILD_DIR -v &&
        mv ~/src/ljt.nightly/latest/log-$TRAVIS_OS_NAME.txt ~/src/ljt.nightly/latest/files/;
      fi
    fi
  - if [ "${BUILD_OFFICIAL:-}" == "" ]; then
      mkdir build &&
      pushd build &&
      cmake -G"Unix Makefiles" -DCMAKE_BUILD_TYPE=$CMAKE_BUILD_TYPE "-DCMAKE_C_FLAGS_RELWITHDEBINFO=$CFLAGS_RELWITHDEBINFO" $CMAKE_FLAGS .. &&
      export NUMCPUS=`grep -c '^processor' /proc/cpuinfo` &&
      make -j$NUMCPUS --load-average=$NUMCPUS &&
      make test &&
      if [[ ! "${CMAKE_FLAGS[0]}" =~ "WITH_12BIT" &&
            ! "${CMAKE_FLAGS[0]}" =~ "WITH_SIMD" ]]; then
        JSIMD_FORCESSE2=1 make test &&
        cmake -DFLOATTEST=32bit .. &&
        JSIMD_FORCENONE=1 make test;
      fi &&
      popd;
    fi

after_failure:
  - if [ "${BUILD_OFFICIAL:-}" == "" ]; then
      if [ -f $TRAVIS_BUILD_DIR/build/config.log ]; then
        cat $TRAVIS_BUILD_DIR/build/config.log;
      fi
    fi

deploy:
  - provider: s3
    bucket: libjpeg-turbo-pr
    access_key_id:
      secure: bmFEt4H90/oR/LiN9XI+G26Pd6hiyrTw3+Vg3lS4ynwAYk33weApaVM8CyzQTgIhGSPzFStqVm9fTrb3RmrYP/PnNS+/surOeWLkH2DMRxvc0qmetBuNx1+vAN7FUkY8MO/u5uE9WXHAdp4e64pXcLXEbKmh+wgDm72b35WmMxErtHsGbpqy+j47rQkY4BJGi7XQzjjafaamfm4PzitsjkYYsgX8KLI16jyJEIirvyDHCPTn9wKR/jSjelDl+xTlgZGuCqmLCBW8f6JgycIspWjcYfO4WpWvkbnnI2sl3rCMPvOYc4wHe8SwzG0l4tM1PblZZDRcU7vjE15PmNf1Xfq9Vx3RpgBJv+UBNL/Vn0rKdpUCeEcfC12hxrske8DWpV6waBiDivjQJreE+YRXqa5YBhV/EdkoKYCqafnJvRASlOko9evje8F9KXTNsIGTT1HPmU9QM9WoJwLs/Xa3t09EmA2IjhcuAvvUmwCTuBBQVAlDjExiTT3Zhc9IYZDD92JgpAYLgridtzR87ElOxKhTkR4PowdI6UiLYArPjMFTjoz5Rivb9qNpbLaQC8HCYgLWxpWtUTzlW/9rM8izHpF8ySFHjO6E2aA9OJFc0tcbEGwAs2jLGD01OduU+DbBfsIkW0EgfXCPbD3FVgHsn3tkuzgO/bg20SM7uuCEYKQ=
    secret_access_key:
      secure: mrkOpEtqd2dEmi/qNJyX9vkME+6xgVBnXaRETKF7jT+flcQCQ0ayQkRkMV7lzGqq44XFg+n6Cpfn6oW0gH9RNdcC8YQvFP+kgzPx6nw6V/M31Vz6ySapJf59HBzVevf0NJkr0/1JoWsp1iq4IoN10WPzsCXZB55Io3Cf7DgpR+yiyBlWOctDfNdjJ97Juw3ENE80MHDf0fVqdUOIknQka1p68yAGkjar9kc2Oe7o94RzzmoqEn8tuFumiBQjIcuVRALsKqz+eIxBNgkL3BF9shVyRjOWLAeBhMPVFxZs5Dgd4ECbvU0i33gfmje3d6qqcw78N2lZaLefoVvWol3pOzVO133ewOSY9/lmpqEiRUU2ohEe8T4aSoS7posBW42itUTO4Y5w+eVOnHsm4sRQaI+/AXWTe7GPel+P8Qbe8Ya10A5gnpoag7o3raRDcHx+/qaZw1Af/u4XiAOYz3be3U90Qc+YMc/kS5i8BH0GXBbSfaWQ00CwRFlZQ3n1xUqmjC2CmjZTki3W/p7mEt0DjhcH9ZIXscK603sCC+mF6pEd9019k5fG/8fr2Y4Ptai9kd3BxZJCX9/jSoMfWOBbgkA5bRgHU0xrAj+p49qD6Ej9Xr8GE3+uebz3sEuhSFRnCKwKoOHOemfgevfO2y/jQXP677WPf3xQX7bVDfTFSHU=
    acl: public_read
    local-dir: $HOME/src/ljt.nightly/latest/files
    upload-dir: $TRAVIS_BRANCH/$TRAVIS_OS_NAME
    on:
      repo: libjpeg-turbo/libjpeg-turbo
      branch: master
      condition: -n "$BUILD_OFFICIAL"
  - provider: s3
    bucket: libjpeg-turbo-pr
    access_key_id:
      secure: bmFEt4H90/oR/LiN9XI+G26Pd6hiyrTw3+Vg3lS4ynwAYk33weApaVM8CyzQTgIhGSPzFStqVm9fTrb3RmrYP/PnNS+/surOeWLkH2DMRxvc0qmetBuNx1+vAN7FUkY8MO/u5uE9WXHAdp4e64pXcLXEbKmh+wgDm72b35WmMxErtHsGbpqy+j47rQkY4BJGi7XQzjjafaamfm4PzitsjkYYsgX8KLI16jyJEIirvyDHCPTn9wKR/jSjelDl+xTlgZGuCqmLCBW8f6JgycIspWjcYfO4WpWvkbnnI2sl3rCMPvOYc4wHe8SwzG0l4tM1PblZZDRcU7vjE15PmNf1Xfq9Vx3RpgBJv+UBNL/Vn0rKdpUCeEcfC12hxrske8DWpV6waBiDivjQJreE+YRXqa5YBhV/EdkoKYCqafnJvRASlOko9evje8F9KXTNsIGTT1HPmU9QM9WoJwLs/Xa3t09EmA2IjhcuAvvUmwCTuBBQVAlDjExiTT3Zhc9IYZDD92JgpAYLgridtzR87ElOxKhTkR4PowdI6UiLYArPjMFTjoz5Rivb9qNpbLaQC8HCYgLWxpWtUTzlW/9rM8izHpF8ySFHjO6E2aA9OJFc0tcbEGwAs2jLGD01OduU+DbBfsIkW0EgfXCPbD3FVgHsn3tkuzgO/bg20SM7uuCEYKQ=
    secret_access_key:
      secure: mrkOpEtqd2dEmi/qNJyX9vkME+6xgVBnXaRETKF7jT+flcQCQ0ayQkRkMV7lzGqq44XFg+n6Cpfn6oW0gH9RNdcC8YQvFP+kgzPx6nw6V/M31Vz6ySapJf59HBzVevf0NJkr0/1JoWsp1iq4IoN10WPzsCXZB55Io3Cf7DgpR+yiyBlWOctDfNdjJ97Juw3ENE80MHDf0fVqdUOIknQka1p68yAGkjar9kc2Oe7o94RzzmoqEn8tuFumiBQjIcuVRALsKqz+eIxBNgkL3BF9shVyRjOWLAeBhMPVFxZs5Dgd4ECbvU0i33gfmje3d6qqcw78N2lZaLefoVvWol3pOzVO133ewOSY9/lmpqEiRUU2ohEe8T4aSoS7posBW42itUTO4Y5w+eVOnHsm4sRQaI+/AXWTe7GPel+P8Qbe8Ya10A5gnpoag7o3raRDcHx+/qaZw1Af/u4XiAOYz3be3U90Qc+YMc/kS5i8BH0GXBbSfaWQ00CwRFlZQ3n1xUqmjC2CmjZTki3W/p7mEt0DjhcH9ZIXscK603sCC+mF6pEd9019k5fG/8fr2Y4Ptai9kd3BxZJCX9/jSoMfWOBbgkA5bRgHU0xrAj+p49qD6Ej9Xr8GE3+uebz3sEuhSFRnCKwKoOHOemfgevfO2y/jQXP677WPf3xQX7bVDfTFSHU=
    acl: public_read
    local-dir: $HOME/src/ljt.nightly/latest/files
    upload-dir: $TRAVIS_BRANCH/$TRAVIS_OS_NAME
    on:
      repo: libjpeg-turbo/libjpeg-turbo
      branch: dev
      condition: -n "$BUILD_OFFICIAL"
