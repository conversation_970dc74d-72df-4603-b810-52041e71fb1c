cmake_minimum_required(VERSION 3.10)

project(libjpeg)

set(libjpeg_sources
  jcapimin.c
  jcapistd.c
  jccoefct.c
  jccolor.c
  jcdctmgr.c
  jchuff.c
  jcinit.c
  jcmainct.c
  jcmarker.c
  jcmaster.c
  jcomapi.c
  jcparam.c
  jcphuff.c
  jcprepct.c
  jcsample.c
  jctrans.c
  jdapimin.c
  jdapistd.c
  jdatadst.c
  jdatasrc.c
  jdcoefct.c
  jdcolor.c
  jddctmgr.c
  jdhuff.c
  jdinput.c
  jdmainct.c
  jdmarker.c
  jdmaster.c
  jdmerge.c
  jdphuff.c
  jdpostct.c
  jdsample.c
  jdtrans.c
  jerror.c
  jfdctflt.c
  jfdctfst.c
  jfdctint.c
  jidctflt.c
  jidctfst.c
  jidctint.c
  jidctred.c
  jquant1.c
  jquant2.c
  jutils.c
  jmemmgr.c
  jmemnobs.c
  jdarith.c
  jaricom.c
  jsimd_none.c
  jcarith.c
)

# Add the static library
add_library(libjpeg STATIC ${libjpeg_sources})
target_include_directories(libjpeg INTERFACE ${CMAKE_CURRENT_SOURCE_DIR})
if("x${CMAKE_C_COMPILER_ID}" STREQUAL "xARMCC")
  target_compile_options(libjpeg PRIVATE "--diag-suppress=546")
endif()

