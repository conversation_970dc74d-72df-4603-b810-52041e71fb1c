IJG JPEG LIBRARY:  CODING RULES

This file was part of the Independent JPEG Group's software:
Copyright (C) 1991-1996, <PERSON>.
It was modified by The libjpeg-turbo Project to include only information
relevant to libjpeg-turbo.
For conditions of distribution and use, see the accompanying README.ijg file.


Since numerous people will be contributing code and bug fixes, it's important
to establish a common coding style.  The goal of using similar coding styles
is much more important than the details of just what that style is.

In general we follow the recommendations of "Recommended C Style and Coding
Standards" revision 6.1 (<PERSON> et al. as modified by <PERSON>, <PERSON> and
<PERSON>).  This document is available in the IJG FTP archive (see
jpeg/doc/cstyle.ms.tbl.Z, or cstyle.txt.Z for those without nroff/tbl).

Block comments should be laid out thusly:

/*
 *  Block comments in this style.
 */

We indent statements in K&R style, e.g.,
        if (test) {
          then-part;
        } else {
          else-part;
        }
with two spaces per indentation level.  (This indentation convention is
handled automatically by GNU Emacs and many other text editors.)

Multi-word names should be written in lower case with underscores, e.g.,
multi_word_name (not multiWordName).  Preprocessor symbols and enum constants
are similar but upper case (MULTI_WORD_NAME).  Names should be unique within
the first fifteen characters.

Note that each function definition must begin with GLOBAL(type), LOCAL(type),
or METHODDEF(type).  These macros expand to "static type" or just "type" as
appropriate.  They provide a readable indication of the routine's usage and
can readily be changed for special needs.  (For instance, special linkage
keywords can be inserted for use in Windows DLLs.)

A similar solution is used for external function declarations (see the EXTERN
macro.)


The JPEG library is intended to be used within larger programs.  Furthermore,
we want it to be reentrant so that it can be used by applications that process
multiple images concurrently.  The following rules support these requirements:

1. Avoid direct use of file I/O, "malloc", error report printouts, etc;
pass these through the common routines provided.

2. Minimize global namespace pollution.  Functions should be declared static
wherever possible.  (Note that our method-based calling conventions help this
a lot: in many modules only the initialization function will ever need to be
called directly, so only that function need be externally visible.)  All
global function names should begin with "jpeg_".

3. Don't use global variables; anything that must be used in another module
should be in the common data structures.

4. Don't use static variables except for read-only constant tables.  Variables
that should be private to a module can be placed into private structures (see
the system architecture document, structure.txt).

5. Source file names should begin with "j" for files that are part of the
library proper; source files that are not part of the library, such as cjpeg.c
and djpeg.c, do not begin with "j".  Keep compression and decompression code in
separate source files --- some applications may want only one half of the
library.

Note: these rules (particularly #4) are not followed religiously in the
modules that are used in cjpeg/djpeg but are not part of the JPEG library
proper.  Those modules are not really intended to be used in other
applications.
