cmake_minimum_required(VERSION 3.10)

set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR arm)

set(ARCH_FLAGS "--cpu=Cortex-R5 --fpu=softvfp --littleend --thumb --apcs=/interwork")

find_program(CMAKE_C_COMPILER NAMES armcc)
set(CMAKE_C_FLAGS_INIT            "${ARCH_FLAGS} --c99 --gnu")
set(CMAKE_C_FLAGS_DEBUG          "-g")
set(CMAKE_C_FLAGS_MINSIZEREL     "-Ospace -DNDEBUG")
set(CMAKE_C_FLAGS_RELEASE        "-O3 -DNDEBUG")
set(CMAKE_C_FLAGS_RELWITHDEBINFO "-Ospace -g")

find_program(CMAKE_CXX_COMPILER NAMES armcc)
set(CMAKE_CXX_FLAGS_INIT          "${ARCH_FLAGS} --cpp11 --gnu --no-exceptions --no-rtti")
set(CMAKE_CXX_FLAGS_DEBUG          "-g")
set(CMAKE_CXX_FLAGS_MINSIZEREL     "-Ospace -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE        "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-Ospace -g")

find_program(CMAKE_OBJCOPY NAMES fromelf)

# Find arelease and ninja
if(DEFINED ENV{MISC_TOOLS_PATH})
  set(MISC_TOOLS_PATH $ENV{MISC_TOOLS_PATH})
else()
  message(FATAL_ERROR "Please set environment variable \"MISC_TOOLS_PATH\" first.")
endif()
list(INSERT CMAKE_PROGRAM_PATH 0 ${MISC_TOOLS_PATH})
find_program(ARELEASE NAMES arelease)

# Don't run the linker on compiler check
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

set(CMAKE_NINJA_FORCE_RESPONSE_FILE 1 CACHE INTERNAL "")
