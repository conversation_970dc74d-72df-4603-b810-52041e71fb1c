#!/bin/bash

if [ "$#" -ne 1 ]; then
    echo "Error: installation target directory not specified."
    return 1
fi

FILES=$(find -type f -name "*.tar" | sed 's/^\.\///')

if [[ "${1:0:1}" == / || "${1:0:2}" == ~[/a-z] ]]; then
    TARGET_DIR=$1
else
    TARGET_DIR=$USER_PWD/$1
fi

mkdir -p $TARGET_DIR
for file in $FILES; do
    echo "Extracting $file..."
    dir=$(dirname $file)
    target_dir=$(realpath $TARGET_DIR)/$dir
    mkdir -p $target_dir
    tar --extract --directory=$target_dir --file=$file
    echo "Done."
done

cp $TARGET_DIR/build/root.mk $TARGET_DIR/Makefile

echo "Finished."
