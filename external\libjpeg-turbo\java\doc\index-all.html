<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Index</title>
<link rel="stylesheet" type="text/css" href="./stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="./org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="./org/libjpegturbo/turbojpeg/package-tree.html">Tree</a></li>
<li><a href="./deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="./help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="./index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="./allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="#_B_">B</a>&nbsp;<a href="#_C_">C</a>&nbsp;<a href="#_D_">D</a>&nbsp;<a href="#_E_">E</a>&nbsp;<a href="#_F_">F</a>&nbsp;<a href="#_G_">G</a>&nbsp;<a href="#_H_">H</a>&nbsp;<a href="#_I_">I</a>&nbsp;<a href="#_J_">J</a>&nbsp;<a href="#_N_">N</a>&nbsp;<a href="#_O_">O</a>&nbsp;<a href="#_P_">P</a>&nbsp;<a href="#_S_">S</a>&nbsp;<a href="#_T_">T</a>&nbsp;<a href="#_Y_">Y</a>&nbsp;<a name="_B_">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#bufSize(int,%20int,%20int)">bufSize(int, int, int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the maximum size of the buffer (in bytes) required to hold a JPEG
 image with the given width, height, and level of chrominance subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#bufSizeYUV(int,%20int,%20int,%20int)">bufSizeYUV(int, int, int, int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the size of the buffer (in bytes) required to hold a YUV planar
 image with the given width, height, and level of chrominance subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#bufSizeYUV(int,%20int,%20int)">bufSizeYUV(int, int, int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use <a href="./org/libjpegturbo/turbojpeg/TJ.html#bufSizeYUV(int,%20int,%20int,%20int)"><code>TJ.bufSizeYUV(int, int, int, int)</code></a> instead.</i></div>
</div>
</dd>
</dl>
<a name="_C_">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#cf">cf</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Custom filter instance</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#close()">close()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Free the native structures associated with this compressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#close()">close()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Free the native structures associated with this decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#compress(byte[],%20int)">compress(byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Compress the uncompressed source image associated with this compressor
 instance and output a JPEG image to the given destination buffer.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#compress(int)">compress(int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Compress the uncompressed source image associated with this compressor
 instance and return a buffer containing a JPEG image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#compress(java.awt.image.BufferedImage,%20byte[],%20int)">compress(BufferedImage, byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#compress(byte[],%20int)"><code>TJCompressor.compress(byte[], int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#compress(java.awt.image.BufferedImage,%20int)">compress(BufferedImage, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#compress(int)"><code>TJCompressor.compress(int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#CS_CMYK">CS_CMYK</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">CMYK colorspace.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#CS_GRAY">CS_GRAY</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Grayscale colorspace.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#CS_RGB">CS_RGB</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">RGB colorspace.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#CS_YCbCr">CS_YCbCr</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">YCbCr colorspace.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#CS_YCCK">CS_YCCK</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">YCCK colorspace.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCustomFilter.html#customFilter(java.nio.ShortBuffer,%20java.awt.Rectangle,%20java.awt.Rectangle,%20int,%20int,%20org.libjpegturbo.turbojpeg.TJTransform)">customFilter(ShortBuffer, Rectangle, Rectangle, int, int, TJTransform)</a></span> - Method in interface org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg">TJCustomFilter</a></dt>
<dd>
<div class="block">A callback function that can be used to modify the DCT coefficients after
 they are losslessly transformed but before they are transcoded to a new
 JPEG image.</div>
</dd>
</dl>
<a name="_D_">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)">decompress(byte[], int, int, int, int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a grayscale, RGB, or CMYK image
 to the given destination buffer.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int)">decompress(byte[], int, int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(byte[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJDecompressor.decompress(byte[], int, int, int, int, int, int, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(int,%20int,%20int,%20int,%20int)">decompress(int, int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance and return a buffer containing the decompressed image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(int[],%20int,%20int,%20int,%20int,%20int,%20int,%20int)">decompress(int[], int, int, int, int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a grayscale, RGB, or CMYK image
 to the given destination buffer.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(java.awt.image.BufferedImage,%20int)">decompress(BufferedImage, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and output a decompressed/decoded image to
 the given <code>BufferedImage</code> instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompress(int,%20int,%20int,%20int)">decompress(int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image or decode the YUV source image associated
 with this decompressor instance and return a <code>BufferedImage</code>
 instance containing the decompressed/decoded image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)">decompressToYUV(YUVImage, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a YUV planar image and store it in the given
 <code>YUVImage</code> instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(byte[],%20int)">decompressToYUV(byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use <a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>TJDecompressor.decompressToYUV(YUVImage, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int[],%20int,%20int)">decompressToYUV(int, int[], int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a set of Y, U (Cb), and V (Cr) image planes and return a
 <code>YUVImage</code> instance containing the decompressed image planes.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int,%20int,%20int)">decompressToYUV(int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Decompress the JPEG source image associated with this decompressor
 instance into a unified YUV planar image buffer and return a
 <code>YUVImage</code> instance containing the decompressed image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int)">decompressToYUV(int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use <a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#decompressToYUV(int,%20int,%20int,%20int)"><code>TJDecompressor.decompressToYUV(int, int, int, int)</code></a> instead.</i></div>
</div>
</dd>
</dl>
<a name="_E_">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)">encodeYUV(YUVImage, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into a YUV planar image and store it in the given
 <code>YUVImage</code> instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)">encodeYUV(byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(org.libjpegturbo.turbojpeg.YUVImage,%20int)"><code>TJCompressor.encodeYUV(YUVImage, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)">encodeYUV(int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into a unified YUV planar image buffer and return a
 <code>YUVImage</code> instance containing the encoded image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int[],%20int)">encodeYUV(int[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Encode the uncompressed source image associated with this compressor
 instance into separate Y, U (Cb), and V (Cr) image planes and return a
 <code>YUVImage</code> instance containing the encoded image planes.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int)">encodeYUV(int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>TJCompressor.encodeYUV(int, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(java.awt.image.BufferedImage,%20byte[],%20int)">encodeYUV(BufferedImage, byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(byte[],%20int)"><code>TJCompressor.encodeYUV(byte[], int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(java.awt.image.BufferedImage,%20int)">encodeYUV(BufferedImage, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(BufferedImage, int, int, int, int)</code></a> and
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#encodeYUV(int,%20int)"><code>TJCompressor.encodeYUV(int, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html#equals(org.libjpegturbo.turbojpeg.TJScalingFactor)">equals(TJScalingFactor)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg">TJScalingFactor</a></dt>
<dd>
<div class="block">Returns true or false, depending on whether this instance and
 <code>other</code> have the same numerator and denominator.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#ERR_FATAL">ERR_FATAL</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The error was fatal and non-recoverable.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#ERR_WARNING">ERR_WARNING</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The error was non-fatal and recoverable, but the image may still be
 corrupt.</div>
</dd>
</dl>
<a name="_F_">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#finalize()">finalize()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#finalize()">finalize()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_ACCURATEDCT">FLAG_ACCURATEDCT</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Use the most accurate DCT/IDCT algorithm available in the underlying
 codec.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_BOTTOMUP">FLAG_BOTTOMUP</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The uncompressed source/destination image is stored in bottom-up (Windows,
 OpenGL) order, not top-down (X11) order.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_FASTDCT">FLAG_FASTDCT</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Use the fastest DCT/IDCT algorithm available in the underlying codec.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_FASTUPSAMPLE">FLAG_FASTUPSAMPLE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">When decompressing an image that was compressed using chrominance
 subsampling, use the fastest chrominance upsampling algorithm available in
 the underlying codec.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCEMMX">FLAG_FORCEMMX</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span></div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCESSE">FLAG_FORCESSE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span></div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCESSE2">FLAG_FORCESSE2</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span></div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_FORCESSE3">FLAG_FORCESSE3</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span></div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_PROGRESSIVE">FLAG_PROGRESSIVE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Use progressive entropy coding in JPEG images generated by compression and
 transform operations.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#FLAG_STOPONWARNING">FLAG_STOPONWARNING</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Immediately discontinue the current compression/decompression/transform
 operation if the underlying codec throws a warning (non-fatal error).</div>
</dd>
</dl>
<a name="_G_">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getAlphaOffset(int)">getAlphaOffset(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">For the given pixel format, returns the number of bytes that the alpha
 component is offset from the start of the pixel.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getBlueOffset(int)">getBlueOffset(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">For the given pixel format, returns the number of bytes that the blue
 component is offset from the start of the pixel.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getBuf()">getBuf()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the YUV image buffer (if this image is stored in a unified
 buffer rather than separate image planes.)</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getColorspace()">getColorspace()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the colorspace used in the source image (JPEG or YUV) associated
 with this decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#getCompressedSize()">getCompressedSize()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Returns the size of the image (in bytes) generated by the most recent
 compress operation.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html#getDenom()">getDenom()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg">TJScalingFactor</a></dt>
<dd>
<div class="block">Returns denominator</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJException.html#getErrorCode()">getErrorCode()</a></span> - Method in exception org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></dt>
<dd>
<div class="block">Returns a code (one of <a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg"><code>TJ.ERR_*</code></a>) indicating the severity of the
 last error.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getGreenOffset(int)">getGreenOffset(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">For the given pixel format, returns the number of bytes that the green
 component is offset from the start of the pixel.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getHeight()">getHeight()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the height of the source image (JPEG or YUV) associated with this
 decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getHeight()">getHeight()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the height of the YUV image (or subregion.)</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getJPEGBuf()">getJPEGBuf()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the JPEG image buffer associated with this decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getJPEGSize()">getJPEGSize()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the size of the JPEG image (in bytes) associated with this
 decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getMCUHeight(int)">getMCUHeight(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the MCU block height for the given level of chrominance
 subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getMCUWidth(int)">getMCUWidth(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the MCU block width for the given level of chrominance
 subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html#getNum()">getNum()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg">TJScalingFactor</a></dt>
<dd>
<div class="block">Returns numerator</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getOffsets()">getOffsets()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the offsets (in bytes) of each plane within the planes of a larger
 YUV image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getPad()">getPad()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the line padding used in the YUV image buffer (if this image is
 stored in a unified buffer rather than separate image planes.)</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getPixelSize(int)">getPixelSize(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the pixel size (in bytes) for the given pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getPlanes()">getPlanes()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the YUV image planes.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getRedOffset(int)">getRedOffset(int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">For the given pixel format, returns the number of bytes that the red
 component is offset from the start of the pixel.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html#getScaled(int)">getScaled(int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg">TJScalingFactor</a></dt>
<dd>
<div class="block">Returns the scaled value of <code>dimension</code>.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledHeight(int,%20int)">getScaledHeight(int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the height of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getScaledWidth(int,%20int)">getScaledWidth(int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the width of the largest scaled-down image that the TurboJPEG
 decompressor can generate without exceeding the desired image width and
 height.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#getScalingFactors()">getScalingFactors()</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns a list of fractional scaling factors that the JPEG decompressor in
 this implementation of TurboJPEG supports.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getSize()">getSize()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the size (in bytes) of the YUV image buffer (if this image is
 stored in a unified buffer rather than separate image planes.)</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getStrides()">getStrides()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the number of bytes per line of each plane in the YUV image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getSubsamp()">getSubsamp()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the level of chrominance subsampling used in the source image
 (JPEG or YUV) associated with this decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getSubsamp()">getSubsamp()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the level of chrominance subsampling used in the YUV image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#getTransformedSizes()">getTransformedSizes()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dt>
<dd>
<div class="block">Returns an array containing the sizes of the transformed JPEG images
 generated by the most recent transform operation.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#getWidth()">getWidth()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Returns the width of the source image (JPEG or YUV) associated with this
 decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#getWidth()">getWidth()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Returns the width of the YUV image (or subregion.)</div>
</dd>
</dl>
<a name="_H_">
<!--   -->
</a>
<h2 class="title">H</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#handle">handle</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#handle">handle</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="_I_">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html#isOne()">isOne()</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg">TJScalingFactor</a></dt>
<dd>
<div class="block">Returns true or false, depending on whether this instance is equal to
 1/1.</div>
</dd>
</dl>
<a name="_J_">
<!--   -->
</a>
<h2 class="title">J</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegBuf">jpegBuf</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegBufSize">jpegBufSize</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegColorspace">jpegColorspace</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegHeight">jpegHeight</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegSubsamp">jpegSubsamp</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#jpegWidth">jpegWidth</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="_N_">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#NUMCS">NUMCS</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The number of JPEG colorspaces</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#NUMERR">NUMERR</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The number of error codes</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#NUMOP">NUMOP</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">The number of lossless transform operations</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#NUMPF">NUMPF</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The number of pixel formats</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#NUMSAMP">NUMSAMP</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">The number of chrominance subsampling options</div>
</dd>
</dl>
<a name="_O_">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#op">op</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Transform operation (one of <code>OP_*</code>)</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_HFLIP">OP_HFLIP</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Flip (mirror) image horizontally.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_NONE">OP_NONE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Do not transform the position of the image pixels.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_ROT180">OP_ROT180</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Rotate image 180 degrees.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_ROT270">OP_ROT270</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Rotate image counter-clockwise by 90 degrees.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_ROT90">OP_ROT90</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Rotate image clockwise by 90 degrees.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_TRANSPOSE">OP_TRANSPOSE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Transpose image (flip/mirror along upper left to lower right axis).</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_TRANSVERSE">OP_TRANSVERSE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Transverse transpose image (flip/mirror along upper right to lower left
 axis).</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OP_VFLIP">OP_VFLIP</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Flip (mirror) image vertically.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_COPYNONE">OPT_COPYNONE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will prevent <a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> from copying any extra markers (including EXIF
 and ICC profile data) from the source image to the output image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_CROP">OPT_CROP</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will enable lossless cropping.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_GRAY">OPT_GRAY</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will discard the color data in the input image and produce
 a grayscale output image.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_NOOUTPUT">OPT_NOOUTPUT</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will prevent <a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> from outputting a JPEG image for this
 particular transform.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PERFECT">OPT_PERFECT</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will cause <a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)"><code>TJTransformer.transform()</code></a> to throw an exception if the transform is not
 perfect.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_PROGRESSIVE">OPT_PROGRESSIVE</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will enable progressive entropy coding in the output image
 generated by this particular transform.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#OPT_TRIM">OPT_TRIM</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">This option will discard any partial MCU blocks that cannot be
 transformed.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#options">options</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Transform options (bitwise OR of one or more of <code>OPT_*</code>)</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a> - package org.libjpegturbo.turbojpeg</dt>
<dd>&nbsp;</dd>
</dl>
<a name="_P_">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_ABGR">PF_ABGR</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">ABGR pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_ARGB">PF_ARGB</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">ARGB pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_BGR">PF_BGR</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">BGR pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_BGRA">PF_BGRA</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">BGRA pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_BGRX">PF_BGRX</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">BGRX pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_CMYK">PF_CMYK</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">CMYK pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_GRAY">PF_GRAY</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Grayscale pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_RGB">PF_RGB</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">RGB pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_RGBA">PF_RGBA</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">RGBA pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_RGBX">PF_RGBX</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">RGBX pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_XBGR">PF_XBGR</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">XBGR pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#PF_XRGB">PF_XRGB</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">XRGB pixel format.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#planeHeight(int,%20int,%20int)">planeHeight(int, int, int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the plane height of a YUV image plane with the given parameters.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#planeSizeYUV(int,%20int,%20int,%20int,%20int)">planeSizeYUV(int, int, int, int, int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the size of the buffer (in bytes) required to hold a YUV image
 plane with the given parameters.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#planeWidth(int,%20int,%20int)">planeWidth(int, int, int)</a></span> - Static method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Returns the plane width of a YUV image plane with the given parameters.</div>
</dd>
</dl>
<a name="_S_">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#SAMP_411">SAMP_411</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">4:1:1 chrominance subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#SAMP_420">SAMP_420</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">4:2:0 chrominance subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#SAMP_422">SAMP_422</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">4:2:2 chrominance subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#SAMP_440">SAMP_440</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">4:4:0 chrominance subsampling.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#SAMP_444">SAMP_444</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">4:4:4 chrominance subsampling (no chrominance subsampling).</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJ.html#SAMP_GRAY">SAMP_GRAY</a></span> - Static variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg">TJ</a></dt>
<dd>
<div class="block">Grayscale.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#setBuf(byte[][],%20int[],%20int,%20int[],%20int,%20int)">setBuf(byte[][], int[], int, int[], int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Assign a set of image planes to this <code>YUVImage</code> instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#setBuf(byte[],%20int,%20int,%20int,%20int)">setBuf(byte[], int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Assign a unified image buffer to this <code>YUVImage</code> instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#setJPEGImage(byte[],%20int)">setJPEGImage(byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use <a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(byte[],%20int)"><code>TJDecompressor.setSourceImage(byte[], int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setJPEGQuality(int)">setJPEGQuality(int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Set the JPEG image quality level for subsequent compress operations.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)">setSourceImage(byte[], int, int, int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Associate an uncompressed RGB, grayscale, or CMYK source image with this
 compressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int)">setSourceImage(byte[], int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJCompressor.setSourceImage(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)">setSourceImage(BufferedImage, int, int, int, int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Associate an uncompressed RGB or grayscale source image with this
 compressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSourceImage(org.libjpegturbo.turbojpeg.YUVImage)">setSourceImage(YUVImage)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Associate an uncompressed YUV planar source image with this compressor
 instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(byte[],%20int)">setSourceImage(byte[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Associate the JPEG image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with this decompressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#setSourceImage(org.libjpegturbo.turbojpeg.YUVImage)">setSourceImage(YUVImage)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Associate the specified YUV planar source image with this decompressor
 instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#setSubsamp(int)">setSubsamp(int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Set the level of chrominance subsampling for subsequent compress/encode
 operations.</div>
</dd>
</dl>
<a name="_T_">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><a href="./org/libjpegturbo/turbojpeg/TJ.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJ</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">TurboJPEG utility class (cannot be instantiated)</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJCompressor</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">TurboJPEG compressor</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor()">TJCompressor()</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG compressor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int,%20int,%20int)">TJCompressor(byte[], int, int, int, int, int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG compressor instance and associate the uncompressed
 source image stored in <code>srcImage</code> with the newly created
 instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int)">TJCompressor(byte[], int, int, int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block"><span class="strong">Deprecated.</span>
<div class="block"><i>Use
 <a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(byte[],%20int,%20int,%20int,%20int,%20int,%20int)"><code>TJCompressor.TJCompressor(byte[], int, int, int, int, int, int)</code></a> instead.</i></div>
</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJCompressor.html#TJCompressor(java.awt.image.BufferedImage,%20int,%20int,%20int,%20int)">TJCompressor(BufferedImage, int, int, int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJCompressor.html" title="class in org.libjpegturbo.turbojpeg">TJCompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG compressor instance and associate the uncompressed
 source image stored in <code>srcImage</code> with the newly created
 instance.</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJCustomFilter.html" title="interface in org.libjpegturbo.turbojpeg"><span class="strong">TJCustomFilter</span></a> - Interface in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">Custom filter callback interface</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJDecompressor</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">TurboJPEG decompressor</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor()">TJDecompressor()</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG decompresssor instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor(byte[])">TJDecompressor(byte[])</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG decompressor instance and associate the JPEG source
 image stored in <code>jpegImage</code> with the newly created instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor(byte[],%20int)">TJDecompressor(byte[], int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG decompressor instance and associate the JPEG source
 image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with the newly created instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#TJDecompressor(org.libjpegturbo.turbojpeg.YUVImage)">TJDecompressor(YUVImage)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>
<div class="block">Create a TurboJPEG decompressor instance and associate the YUV planar
 source image stored in <code>yuvImage</code> with the newly created
 instance.</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJException</span></a> - Exception in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJException.html#TJException()">TJException()</a></span> - Constructor for exception org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJException.html#TJException(java.lang.String,%20java.lang.Throwable)">TJException(String, Throwable)</a></span> - Constructor for exception org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJException.html#TJException(java.lang.String)">TJException(String)</a></span> - Constructor for exception org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJException.html#TJException(java.lang.String,%20int)">TJException(String, int)</a></span> - Constructor for exception org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJException.html#TJException(java.lang.Throwable)">TJException(Throwable)</a></span> - Constructor for exception org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJException.html" title="class in org.libjpegturbo.turbojpeg">TJException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJScalingFactor</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">Fractional scaling factor</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html#TJScalingFactor(int,%20int)">TJScalingFactor(int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJScalingFactor.html" title="class in org.libjpegturbo.turbojpeg">TJScalingFactor</a></dt>
<dd>
<div class="block">Create a TurboJPEG scaling factor instance.</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJTransform</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">Lossless transform parameters</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform()">TJTransform()</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Create a new lossless transform instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform(int,%20int,%20int,%20int,%20int,%20int,%20org.libjpegturbo.turbojpeg.TJCustomFilter)">TJTransform(int, int, int, int, int, int, TJCustomFilter)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Create a new lossless transform instance with the given parameters.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransform.html#TJTransform(java.awt.Rectangle,%20int,%20int,%20org.libjpegturbo.turbojpeg.TJCustomFilter)">TJTransform(Rectangle, int, int, TJCustomFilter)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransform.html" title="class in org.libjpegturbo.turbojpeg">TJTransform</a></dt>
<dd>
<div class="block">Create a new lossless transform instance with the given parameters.</div>
</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">TJTransformer</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">TurboJPEG lossless transformer</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#TJTransformer()">TJTransformer()</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dt>
<dd>
<div class="block">Create a TurboJPEG lossless transformer instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#TJTransformer(byte[])">TJTransformer(byte[])</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dt>
<dd>
<div class="block">Create a TurboJPEG lossless transformer instance and associate the JPEG
 image stored in <code>jpegImage</code> with the newly created instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#TJTransformer(byte[],%20int)">TJTransformer(byte[], int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dt>
<dd>
<div class="block">Create a TurboJPEG lossless transformer instance and associate the JPEG
 image of length <code>imageSize</code> bytes stored in
 <code>jpegImage</code> with the newly created instance.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#transform(byte[][],%20org.libjpegturbo.turbojpeg.TJTransform[],%20int)">transform(byte[][], TJTransform[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dt>
<dd>
<div class="block">Losslessly transform the JPEG image associated with this transformer
 instance into one or more JPEG images stored in the given destination
 buffers.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJTransformer.html#transform(org.libjpegturbo.turbojpeg.TJTransform[],%20int)">transform(TJTransform[], int)</a></span> - Method in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJTransformer.html" title="class in org.libjpegturbo.turbojpeg">TJTransformer</a></dt>
<dd>
<div class="block">Losslessly transform the JPEG image associated with this transformer
 instance and return an array of <a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg"><code>TJDecompressor</code></a> instances, each of
 which has a transformed JPEG image associated with it.</div>
</dd>
</dl>
<a name="_Y_">
<!--   -->
</a>
<h2 class="title">Y</h2>
<dl>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvHeight">yuvHeight</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html#yuvImage">yuvImage</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/TJDecompressor.html" title="class in org.libjpegturbo.turbojpeg">TJDecompressor</a></dt>
<dd>&nbsp;</dd>
<dt><a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg"><span class="strong">YUVImage</span></a> - Class in <a href="./org/libjpegturbo/turbojpeg/package-summary.html">org.libjpegturbo.turbojpeg</a></dt>
<dd>
<div class="block">This class encapsulates a YUV planar image and the metadata
 associated with it.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#YUVImage(int,%20int[],%20int,%20int)">YUVImage(int, int[], int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Create a new <code>YUVImage</code> instance backed by separate image
 planes, and allocate memory for the image planes.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#YUVImage(int,%20int,%20int,%20int)">YUVImage(int, int, int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Create a new <code>YUVImage</code> instance backed by a unified image
 buffer, and allocate memory for the image buffer.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#YUVImage(byte[][],%20int[],%20int,%20int[],%20int,%20int)">YUVImage(byte[][], int[], int, int[], int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Create a new <code>YUVImage</code> instance from a set of existing image
 planes.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#YUVImage(byte[],%20int,%20int,%20int,%20int)">YUVImage(byte[], int, int, int, int)</a></span> - Constructor for class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>
<div class="block">Create a new <code>YUVImage</code> instance from an existing unified image
 buffer.</div>
</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvOffsets">yuvOffsets</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvPad">yuvPad</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvPlanes">yuvPlanes</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvStrides">yuvStrides</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvSubsamp">yuvSubsamp</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="./org/libjpegturbo/turbojpeg/YUVImage.html#yuvWidth">yuvWidth</a></span> - Variable in class org.libjpegturbo.turbojpeg.<a href="./org/libjpegturbo/turbojpeg/YUVImage.html" title="class in org.libjpegturbo.turbojpeg">YUVImage</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="#_B_">B</a>&nbsp;<a href="#_C_">C</a>&nbsp;<a href="#_D_">D</a>&nbsp;<a href="#_E_">E</a>&nbsp;<a href="#_F_">F</a>&nbsp;<a href="#_G_">G</a>&nbsp;<a href="#_H_">H</a>&nbsp;<a href="#_I_">I</a>&nbsp;<a href="#_J_">J</a>&nbsp;<a href="#_N_">N</a>&nbsp;<a href="#_O_">O</a>&nbsp;<a href="#_P_">P</a>&nbsp;<a href="#_S_">S</a>&nbsp;<a href="#_T_">T</a>&nbsp;<a href="#_Y_">Y</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="./org/libjpegturbo/turbojpeg/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="./org/libjpegturbo/turbojpeg/package-tree.html">Tree</a></li>
<li><a href="./deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="./help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="./index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="./allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
