def label = "linux && docker"
def description = ""
def gitBaseUrl = "http://sh2-git01.asrmicro.com/git"
def sdkBuilder = 'crane-sdk-prebuilt-builder'
def sdkProject = 'crane/product/crane_modem'
def buildSdk = false
def buildSdkPromoted = true
def repoBranch = 'stable'
def manifestBranch = 'master'

def isTimerTriggered() {
    def build = currentBuild
    currentBuild.upstreamBuilds?.each { b ->
        build = b
    }
    return build.getBuildCauses('hudson.triggers.TimerTrigger$TimerTriggerCause').size() == 1
}

properties([buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '100')),
            durabilityHint('PERFORMANCE_OPTIMIZED'),
            pipelineTriggers([gerrit(gerritProjects: [[branches: [[compareType: 'PLAIN', pattern: 'master'],
                                                                  [compareType: 'PLAIN', pattern: 'stable']],
                                                       compareType: 'ANT',
                                                       disableStrictForbiddenFileVerification: false,
                                                       pattern: 'crane/**']],
                                     triggerOnEvents: [patchsetCreated(), refUpdated()])])])

if (env.GERRIT_PROJECT == sdkProject) {
    buildSdk = true
    if (env.GERRIT_REFSPEC != null) {
        label = 'windows && armcc'
        buildSdkPromoted = false
        repoBranch = 'stable-msys2'
    }
}

node(label) {
    stage('Checkout') {
        if (env.GERRIT_EVENT_TYPE == 'patchset-created') {
            manifestBranch = env.GERRIT_BRANCH
        } else if (env.GERRIT_EVENT_TYPE == 'ref-updated') {
            manifestBranch = env.GERRIT_REFNAME
        } else {
            manifestBranch = 'master'
        }

        checkout([$class: 'RepoScm',
                  currentBranch: true,
                  depth: 2,
                  cleanFirst: true,
                  resetFirst: true,
                  noTags: true,
                  quiet: false,
                  manifestBranch: manifestBranch,
                  manifestRepositoryUrl: "${gitBaseUrl}/crane/manifest.git",
                  repoUrl: "${gitBaseUrl}/tools/repo.git",
                  repoBranch: repoBranch,
                  extraEnvVars: getContext(hudson.EnvVars)])

        def dailyBuild = isTimerTriggered()

        if (env.GERRIT_EVENT_TYPE == 'patchset-created') {
            sh """
                repo download ${GERRIT_PROJECT} ${GERRIT_CHANGE_NUMBER}/${GERRIT_PATCHSET_NUMBER}
            """
            currentBuild.description = 'Owner #' + env.GERRIT_CHANGE_OWNER_NAME
        } else if (env.GERRIT_EVENT_TYPE == 'ref-updated') {
            currentBuild.description = 'Project #' + env.GERRIT_PROJECT
        } else if (dailyBuild) {
            currentBuild.description = 'Daily build'
        }
    }

    stage('Prebuild sdk') {
        def sdkPath = sdkProject - ~/^crane\//
        dir(sdkPath) {
            sh """
                git reset --hard
                git clean -fdx
            """
            if (buildSdk) {
                def built

                if (buildSdkPromoted) {
                    built = build job: sdkBuilder, parameters: [string(name: 'BRANCH', value: manifestBranch)]
                    copyArtifacts projectName: sdkBuilder,
                                  fingerprintArtifacts: true,
                                  selector: specific("${built.number}");
                } else {
                    withEnv(["PS_MODE=LTEGSM", "TARGET_OS=THREADX"]) {
                        def crane_lib_postfix = 'LTEGSM'
                        bat """
                            @echo off
                            echo "Building Crane SDK on nodes with ${label} in PS_MODE=%PS_MODE% TARGET_OS=%TARGET_OS%"
                            call build.bat
                            if %ERRORLEVEL% == 0 goto :next
                            echo "Errors encountered during build."
                            goto :endofscript
                            :next
                            echo "Build completed successfully"
                            exit /b 0
                            :endofscript
                            exit /b -1
                        """

                        sh """
                            set -e
                            CP_OBJLIB_LIST=\$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_$crane_lib_postfix/Arbel_objliblist.txt | \\
                                sed 's/\\s*\$//' | \\
                                sed 's/..\\\\obj_PMD2NONE/\\\\tavor\\\\Arbel\\\\CRANE_SDK_LIB\\\\FP_GENERIC_${crane_lib_postfix}/' | \\
                                sed 's|\\\\|/|g' | sed 's|^\\/||' | \\
                                sed 's|3g_ps/rls/tplgsm/bldstore/hsiupdlibdev/build/lib|tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_$crane_lib_postfix|')
                            sed -i '/^  # MODEM_LIB_${crane_lib_postfix}_BEGIN/,/^  # MODEM_LIB_${crane_lib_postfix}_END/d' CMakeLists.txt
                            sed -i '/^  # MODEM_LIB_${crane_lib_postfix}_LIST/a\\  # MODEM_LIB_${crane_lib_postfix}_BEGIN\\\n  # MODEM_LIB_${crane_lib_postfix}_END' CMakeLists.txt
                            for f in \$CP_OBJLIB_LIST; do
                                sed -i "/^  # MODEM_LIB_${crane_lib_postfix}_END/i\\  \$f" CMakeLists.txt
                            done
                        """
                    }
                }
            } else {
                copyArtifacts projectName: sdkBuilder,
                              fingerprintArtifacts: true,
                              parameters: "BRANCH=${manifestBranch}"
                              selector: lastWithArtifacts()
            }
            if (buildSdkPromoted) {
                sh 'git config --global user.name "Jenkins Builder"'
                sh 'git config --global user.email <EMAIL>'
                sh 'git am --keep-cr 0001-*.patch || { git am --abort; exit 1; }'
            }
        }
        sh """
            rm -rf "${sdkPath}@tmp"
        """
    }

    stage('Build') {
        if (buildSdkPromoted) {
            docker.image('sh2-git01.asrmicro.com:5000/buildsystem/cranebuilder:ubuntu').inside {
                sh """
                    export ARMLMD_LICENSE_FILE=27000@10.38.122.50
                    export PS_MODE=LTEGSM TARGET_OS=THREADX
                    echo "Building Crane Phone Project on nodes with ${label}"
                    make clean
                    make all
                    """
            }
        } else {
            sh """
                export PS_MODE=LTEGSM TARGET_OS=THREADX
                echo "Building Crane Phone Project on nodes with ${label}"
                make clean
                make crane_modem_watch crane_modem_phone
                """
        }
    }

    stage('Archive artifacts') {
        archiveArtifacts 'out/**/pc_simulator_*,out/**/crane_bare_*,out/**/crane_modem_*'
    }
}
